package org.springblade.modules.yjzb.util;

import org.docx4j.Docx4J;
import org.docx4j.fonts.IdentityPlusMapper;
import org.docx4j.fonts.PhysicalFonts;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;

import java.awt.*;
import java.io.InputStream;
import java.io.OutputStream;

public class DocxToPdfConverter {
    static {
        // 预加载中文字体（需放在resources/fonts/目录）
        loadFontResource("/fonts/NSimSun-02.ttf");
        loadFontResource("/fonts/simhei.ttf");
        loadFontResource("/fonts/SimSun-01.ttf");
        loadFontResource("/fonts/simfang.ttf");

    }
    public static void convertDocxToPdf(InputStream docxInput, OutputStream pdfOutput) throws Exception {
        try {
            WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(docxInput);
            IdentityPlusMapper fontMapper = new IdentityPlusMapper();
            // 将逻辑名"仿宋"映射到物理字体
            fontMapper.put("仿宋", PhysicalFonts.get("SimSun"));  // 或尝试"FangSong"
            // 禁用 word-spacing 计算
            wordMLPackage.setFontMapper(fontMapper);  // 应用到文档处理实例
            Docx4J.toPDF(wordMLPackage, pdfOutput);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    private static void loadFontResource(String fontPath) {
        try (InputStream is = DocxToPdfConverter.class.getResourceAsStream(fontPath)) {
            if (is == null) {
                return;
            }
            Font font = Font.createFont(Font.TRUETYPE_FONT, is);
            GraphicsEnvironment.getLocalGraphicsEnvironment().registerFont(font);
        } catch (Exception e) {
            // 不抛出异常，允许程序继续运行
            e.printStackTrace();
        }
    }
}