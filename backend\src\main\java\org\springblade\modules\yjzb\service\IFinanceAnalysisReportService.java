/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisReportEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisReportVO;

import java.util.Map;

/**
 * 财务分析报告列表服务接口
 *
 * <AUTHOR> Assistant
 */
public interface IFinanceAnalysisReportService extends IService<FinanceAnalysisReportEntity> {

    /**
     * 分页查询财务分析报告列表
     *
     * @param page 分页参数
     * @param report 查询条件
     * @return 分页数据
     */
    IPage<FinanceAnalysisReportVO> selectReportPage(IPage<FinanceAnalysisReportVO> page, FinanceAnalysisReportVO report);

    /**
     * 获取报告统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getReportStatistics();

    /**
     * 根据ID查询详情
     *
     * @param id 报告ID
     * @return 报告详情
     */
    FinanceAnalysisReportVO getReportDetail(Long id);

    /**
     * 创建新的分析报告记录
     *
     * @param title 报告标题
     * @param type 报告类型
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 报告记录
     */
    FinanceAnalysisReportEntity createOrUpdateReport(String title, String type, Integer queryYear,
                                                     Integer compareYear, Integer startMonth, Integer endMonth);

    /**
     * 更新报告状态
     *
     * @param reportId 报告ID
     * @param status 状态
     * @param errorMessage 错误信息（可选）
     * @return 更新结果
     */
    boolean updateReportStatus(Long reportId, String status, String errorMessage);

    /**
     * 完成报告生成
     *
     * @param reportId 报告ID
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @param fileSize 文件大小
     * @return 更新结果
     */
    boolean completeReport(Long reportId, String filePath, String fileName, Long fileSize);

    /**
     * 增加下载次数
     *
     * @param id 报告ID
     * @return 更新结果
     */
    boolean incrementDownloadCount(Long id);

    /**
     * 删除报告（逻辑删除）
     *
     * @param ids 报告ID列表
     * @return 删除结果
     */
    boolean deleteReports(String ids);

    /**
     * 生成分析报告
     *
     * @param title 报告标题
     * @param type 报告类型
     * @param queryYear 查询年份
     * @param compareYear 对比年份
     * @param startMonth 开始月份
     * @param endMonth 结束月份
     * @return 报告ID
     */
    Long generateAnalysisReport(String title, String type, Integer queryYear,
                               Integer compareYear, Integer startMonth, Integer endMonth);

    /**
     * 下载报告文件数据
     *
     * @param id 报告ID
     * @return 文件数据字节数组
     */
    byte[] downloadReportData(Long id);
}
