# Word转PDF解决方案指南

## 概述

本系统提供了多种Word转PDF的解决方案，以解决原有docx4j容易卡死的问题。系统会智能选择最佳的转换方案，确保转换的稳定性和质量。

## 转换方案

### 1. Pandoc方案（推荐）⭐⭐⭐⭐⭐

**优点：**
- 转换质量最高，格式保持最好
- 支持复杂的文档结构和样式
- 支持中文字体和排版
- 转换速度快

**缺点：**
- 需要安装Pandoc和LaTeX

**安装方法：**
```bash
# Windows
choco install pandoc miktex

# Ubuntu/Debian
sudo apt-get install pandoc texlive-xetex texlive-fonts-recommended

# CentOS/RHEL
sudo yum install pandoc texlive-xetex

# macOS
brew install pandoc mactex
```

### 2. LibreOffice方案 ⭐⭐⭐⭐

**优点：**
- 转换质量高，兼容性好
- 支持多种文档格式
- 稳定可靠

**缺点：**
- 需要安装LibreOffice
- 首次启动较慢

**安装方法：**
```bash
# Ubuntu/Debian
sudo apt-get install libreoffice

# CentOS/RHEL
sudo yum install libreoffice

# Windows: 下载安装包
# https://www.libreoffice.org/download/

# Docker环境
FROM ubuntu:20.04
RUN apt-get update && apt-get install -y libreoffice
```

### 3. Tika + PDFBox方案 ⭐⭐⭐

**优点：**
- 纯Java实现，无需额外安装
- 部署简单，轻量级
- 不会卡死

**缺点：**
- 转换质量一般
- 复杂格式可能丢失
- 主要提取文本内容

### 4. docx4j方案（回退） ⭐⭐

**优点：**
- 纯Java实现
- 支持复杂格式

**缺点：**
- 容易卡死（已添加超时控制）
- 字体问题
- 性能较差

## 配置说明

在 `application.yml` 中添加配置：

```yaml
word-to-pdf:
  enable-pandoc: true
  enable-libre-office: true
  enable-tika: true
  enable-docx4j: true
  timeout-seconds: 60
  pdf-engine: "xelatex"
  cjk-font: "SimSun"
  margin: "2cm"
```

## 使用方法

系统会自动按优先级选择可用的转换方案：

1. **Pandoc** (如果可用且启用)
2. **LibreOffice** (如果启用)
3. **Tika + PDFBox** (如果启用)
4. **docx4j** (回退方案，如果启用)

## Docker部署

创建包含转换工具的Docker镜像：

```dockerfile
FROM openjdk:11-jre-slim

# 安装LibreOffice和Pandoc
RUN apt-get update && \
    apt-get install -y libreoffice pandoc texlive-xetex texlive-fonts-recommended && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 安装中文字体
RUN apt-get update && \
    apt-get install -y fonts-wqy-microhei fonts-wqy-zenhei && \
    apt-get clean

COPY your-app.jar /app.jar
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 性能优化建议

1. **预热LibreOffice：** 在应用启动时执行一次转换预热
2. **字体缓存：** 确保系统字体正确安装和缓存
3. **临时文件清理：** 定期清理转换过程中的临时文件
4. **并发控制：** 限制同时进行的转换任务数量

## 故障排除

### Pandoc转换失败
- 检查Pandoc是否正确安装：`pandoc --version`
- 检查LaTeX是否安装：`xelatex --version`
- 检查中文字体是否可用

### LibreOffice转换失败
- 检查LibreOffice是否安装：`libreoffice --version`
- 确保有足够的磁盘空间用于临时文件
- 检查文件权限

### 所有方案都失败
- 检查日志中的详细错误信息
- 确保至少启用了Tika方案作为最后的回退
- 检查系统资源（内存、磁盘空间）

## 监控和日志

系统会记录详细的转换日志：

```
INFO  - 开始Word转PDF转换，可用转换器: Pandoc, LibreOffice, Tika+PDFBox, docx4j
INFO  - 使用Pandoc进行Word转PDF转换
INFO  - Word转PDF转换成功，耗时: 2.3秒
```

可以通过日志监控转换成功率和性能。
