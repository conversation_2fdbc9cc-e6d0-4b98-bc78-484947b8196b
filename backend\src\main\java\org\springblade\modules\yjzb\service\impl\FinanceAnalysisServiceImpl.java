package org.springblade.modules.yjzb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.yjzb.mapper.FinanceAnalysisMapper;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisVO;
import org.springblade.modules.yjzb.service.IDifyService;
import org.springblade.modules.yjzb.service.IFinanceAnalysisService;
import org.springblade.modules.yjzb.wrapper.FinanceAnalysisWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 财务分析服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceAnalysisServiceImpl extends BaseServiceImpl<FinanceAnalysisMapper, FinanceAnalysisEntity> implements IFinanceAnalysisService {

    private final FinanceAnalysisMapper financeAnalysisMapper;

    private final IDifyService difyService;

    private final ObjectMapper objectMapper;

    @Value("${dify.api.agentkey.finance-analysis-key:}")
    private String financeAnalysisKey;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * 获取主要经济指标分析数据
     *
     * @param params 查询参数，包含queryYear, compareYear, startMonth, endMonth
     * @return 主要经济指标分析数据
     */
    @Override
    public List<Map<String, Object>> selectMainEconomicIndicators(Map<String, Object> params) {
        // 参数校验
        if (params == null) {
            return new ArrayList<>();
        }
        return financeAnalysisMapper.selectMainEconomicIndicators(params);
    }

    @Override
    public List<Map<String, Object>> selectThreeExpenses(Map<String, Object> params) {
        // 参数校验
        if (params == null) {
            return new ArrayList<>();
        }
        return financeAnalysisMapper.selectThreeExpenses(params);
    }

    @Override
    public List<Map<String, Object>> selectKeyExpenses(Map<String, Object> params) {
        // 参数校验
        if (params == null) {
            return new ArrayList<>();
        }
        return financeAnalysisMapper.selectKeyExpenses(params);
    }

    @Override
    public String analyzeThreeExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) throws JsonProcessingException {
        return analyzeThreeExpenses(queryYear, compareYear, startMonth, endMonth, null);
    }

    @Override
    public String analyzeThreeExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId) throws JsonProcessingException {
        try {
            // 1. 获取三项费用表格数据
            Map<String, Object> params = new HashMap<>();
            params.put("queryYear", queryYear);
            params.put("compareYear", compareYear);
            params.put("startMonth", startMonth);
            params.put("endMonth", endMonth);

            List<Map<String, Object>> threeExpensesData = selectThreeExpenses(params);
            if (threeExpensesData == null || threeExpensesData.isEmpty()) {
                throw new RuntimeException("未获取到三项费用表格数据");
            }

            // 2. 创建或更新三项费用表格记录
            FinanceAnalysisEntity entity = new FinanceAnalysisEntity();
            entity.setName("三项费用表格");
            entity.setType("three_expenses");
            entity.setQueryYear(queryYear);
            entity.setCompareYear(compareYear);
            entity.setStartMonth(startMonth);
            entity.setEndMonth(endMonth);
            entity.setReportId(reportId); // 设置报告ID
            entity.setExecuteStatus("COMPLETED");
            entity.setResult(objectMapper.writeValueAsString(threeExpensesData));
            boolean success = saveOrUpdate("三项费用表格", queryYear, compareYear, startMonth, endMonth, entity);
            if (!success) {
                throw new RuntimeException("保存三项费用表格记录失败");
            }


            // 3. 计算各项指标值
            Map<String, Object> calculatedValues = calculateThreeExpensesIndicatorValues(threeExpensesData, queryYear, compareYear, startMonth, endMonth);

            // 4. 组装输入参数
            Map<String, Object> inputs = new HashMap<>();
            inputs.put("dataList", objectMapper.writeValueAsString(threeExpensesData));
            inputs.put("calculatedValues", objectMapper.writeValueAsString(calculatedValues));
            inputs.put("queryYear", queryYear);
            inputs.put("compareYear", compareYear);
            inputs.put("startMonth", startMonth);
            inputs.put("endMonth", endMonth);
            inputs.put("analysisType", "三项费用支出总体情况分析");

//            // 添加当前时期信息
//            String currentPeriod = queryYear + "年1-" + endMonth + "月";
//            inputs.put("currentPeriod", currentPeriod);
//
//            try {
//                inputs.put("inputParams", objectMapper.writeValueAsString(calculatedValues));
//            } catch (Exception e) {
//                inputs.put("inputParams", String.valueOf(calculatedValues));
//            }

            String userName = AuthUtil.getUserName();

            // 5. 启动Dify工作流
            String workflowRunId = difyService.startWorkflowStreaming(inputs, StringUtil.isNotBlank(userName) ? userName : "yjzb", financeAnalysisKey);

            if (workflowRunId == null) {
                log.error("启动Dify工作流失败，请检查 Dify 配置与权限");
                throw new RuntimeException("启动Dify工作流失败");
            }

            log.info("启动三项费用AI分析：queryYear={}, compareYear={}, period={}-{}, workflowRunId={}",
                    queryYear, compareYear, startMonth, endMonth, workflowRunId);

            // 6. 创建或更新财务分析记录
            String analysisName = "三项费用支出总体情况分析";
            entity = new FinanceAnalysisEntity();
            entity.setName(analysisName);
            entity.setType("three_expenses_analysis");
            entity.setQueryYear(queryYear);
            entity.setCompareYear(compareYear);
            entity.setStartMonth(startMonth);
            entity.setEndMonth(endMonth);
            entity.setReportId(reportId); // 设置报告ID
            entity.setWorkflowRunId(workflowRunId);
            entity.setExecuteStatus("RUNNING");
            entity.setResult("");
            entity.setThinkProcess("");
            entity.setAnswerContent("");

            try {
                entity.setInputParams(objectMapper.writeValueAsString(inputs));
            } catch (Exception e) {
                entity.setInputParams(String.valueOf(inputs));
            }

            // 7. 根据组合主键查询是否已存在记录
            FinanceAnalysisEntity existingEntity = getOneByCompositeKey(analysisName, queryYear, compareYear, startMonth, endMonth);

            if (existingEntity == null) {
                success = save(entity);
            } else {
                entity.setId(existingEntity.getId());
                success = updateById(entity);
            }

            if (!success) {
                throw new RuntimeException("保存三项费用支出总体情况分析记录失败");
            }

            Long financeAnalysisId = entity.getId();

            // 8. 启动后台轮询任务：每10秒查询一次执行结果
            log.info("[三项费用支出总体情况分析-轮询] 启动轮询任务: financeAnalysisId={}, workflowRunId={}, period={}-{}, 间隔=10秒",
                    financeAnalysisId, workflowRunId, startMonth, endMonth);

            final String finalApiKey = financeAnalysisKey;
            final long financeAnalysisIdFinal = financeAnalysisId;
            final String workflowRunIdFinal = workflowRunId;
            final AtomicReference<ScheduledFuture<?>> futureRef = new AtomicReference<>();

            ScheduledFuture<?> scheduled = scheduler.scheduleAtFixedRate(() -> {
                try {
                    log.debug("[三项费用支出总体情况分析-轮询] tick: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                    FinanceAnalysisEntity current = getById(financeAnalysisIdFinal);
                    if (current == null || !"RUNNING".equalsIgnoreCase(current.getExecuteStatus())) {
                        // 已非运行状态，结束轮询
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null) {
                            f.cancel(false);
                        }
                        log.info("[三项费用支出总体情况分析-轮询] 停止轮询: financeAnalysisId={}, 当前状态={}", financeAnalysisIdFinal,
                                current == null ? "null" : current.getExecuteStatus());
                        return;
                    }

                    String detail = difyService.getWorkflowRunDetail(workflowRunIdFinal, finalApiKey);
                    if (detail == null || detail.isBlank()) {
                        log.warn("[三项费用支出总体情况分析-轮询] 未获取到工作流详情: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal,
                                workflowRunIdFinal);
                        return;
                    }

                    // 从返回内容中提取 status
                    String status = extractStatus(detail);
                    String lower = status == null ? null : status.toLowerCase();
                    log.info("[三项费用支出总体情况分析-轮询] 查询结果: financeAnalysisId={}, workflowRunId={}, status={}", financeAnalysisIdFinal,
                            workflowRunIdFinal, lower);

                    if ("succeeded".equals(lower) || "completed".equals(lower)) {
                        // 提取输出文本
                        String outputText = extractOutputText(detail);
                        current.setResult(outputText != null ? outputText : detail);
                        current.setExecuteStatus("COMPLETED");

                        // 解析Dify返回结果，提取think_process和answer_content
                        parseDifyWorkflowResult(current, outputText != null ? outputText : detail);

                        updateById(current);
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null) {
                            f.cancel(false);
                        }
                        log.info("[三项费用支出总体情况分析-轮询] 已完成: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                    } else if ("failed".equals(lower) || "error".equals(lower)) {
                        current.setResult("FAILED: " + detail);
                        current.setExecuteStatus("FAILED");
                        updateById(current);
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null) {
                            f.cancel(false);
                        }
                        log.info("[三项费用支出总体情况分析-轮询] 已失败: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                    }
                } catch (Exception ex) {
                    log.error("轮询Dify工作流结果异常, workflowRunId={}", workflowRunIdFinal, ex);
                }
            }, 10, 10, TimeUnit.SECONDS); // 延迟10秒执行
            futureRef.set(scheduled);

            return workflowRunId;

        } catch (Exception e) {
            log.error("分析三项费用失败", e);
            throw new RuntimeException("分析失败：" + e.getMessage(), e);
        }
    }



    @Override
    public String analyzeKeyExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) throws JsonProcessingException {
        return analyzeKeyExpenses(queryYear, compareYear, startMonth, endMonth, null);
    }

    @Override
    public String analyzeKeyExpenses(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId) throws JsonProcessingException {
        try {
            // 1. 获取重点费用支出情况数据
            Map<String, Object> params = new HashMap<>();
            params.put("queryYear", queryYear);
            params.put("compareYear", compareYear);
            params.put("startMonth", startMonth);
            params.put("endMonth", endMonth);

            List<Map<String, Object>> keyExpensesData = selectKeyExpenses(params);
            if (keyExpensesData == null || keyExpensesData.isEmpty()) {
                throw new RuntimeException("未获取到重点费用支出情况表格数据");
            }

            // 2. 创建或更新重点费用支出情况记录
            FinanceAnalysisEntity entity = new FinanceAnalysisEntity();
            entity.setName("重点费用支出情况表格");
            entity.setType("key_expenses");
            entity.setQueryYear(queryYear);
            entity.setCompareYear(compareYear);
            entity.setStartMonth(startMonth);
            entity.setEndMonth(endMonth);
            entity.setReportId(reportId); // 设置报告ID
            entity.setExecuteStatus("COMPLETED");
            entity.setResult(objectMapper.writeValueAsString(keyExpensesData));
            boolean success = saveOrUpdate("重点费用支出情况表格", queryYear, compareYear, startMonth, endMonth, entity);
            if (!success) {
                throw new RuntimeException("保存重点费用支出情况表格记录失败");
            }

            // 3. 计算各项指标值
            Map<String, Object> calculatedValues = calculateKeyExpensesIndicatorValues(keyExpensesData, queryYear, compareYear, startMonth, endMonth);

            // 4. 组装输入参数
            Map<String, Object> inputs = new HashMap<>();
            inputs.put("dataList", objectMapper.writeValueAsString(keyExpensesData));
            inputs.put("calculatedValues", objectMapper.writeValueAsString(calculatedValues));
            inputs.put("queryYear", queryYear);
            inputs.put("compareYear", compareYear);
            inputs.put("startMonth", startMonth);
            inputs.put("endMonth", endMonth);
            inputs.put("analysisType", "重点费用支出情况分析");

//            // 添加当前时期信息
//            String currentPeriod = queryYear + "年1-" + endMonth + "月";
//            inputs.put("currentPeriod", currentPeriod);
//
//            // 添加截至日期
//            int lastDay = getLastDayOfMonth(queryYear, endMonth);
//            String deadlineDate = queryYear + "年" + endMonth + "月" + lastDay + "日";
//            inputs.put("deadlineDate", deadlineDate);
//
//            try {
//                inputs.put("inputParams", objectMapper.writeValueAsString(calculatedValues));
//            } catch (Exception e) {
//                inputs.put("inputParams", String.valueOf(calculatedValues));
//            }

            String userName = AuthUtil.getUserName();

            // 5. 启动Dify工作流
            String workflowRunId = difyService.startWorkflowStreaming(inputs, StringUtil.isNotBlank(userName) ? userName : "yjzb", financeAnalysisKey);

            if (workflowRunId == null) {
                log.error("启动Dify工作流失败，请检查 Dify 配置与权限");
                throw new RuntimeException("启动Dify工作流失败");
            }

            log.info("启动重点费用支出情况AI分析：queryYear={}, compareYear={}, period={}-{}, workflowRunId={}",
                    queryYear, compareYear, startMonth, endMonth, workflowRunId);

            // 6. 创建或更新财务分析记录
            String analysisName = "重点费用支出情况分析";
            entity = new FinanceAnalysisEntity();
            entity.setName(analysisName);
            entity.setType("key_expenses_analysis");
            entity.setQueryYear(queryYear);
            entity.setCompareYear(compareYear);
            entity.setStartMonth(startMonth);
            entity.setEndMonth(endMonth);
            entity.setReportId(reportId); // 设置报告ID
            entity.setWorkflowRunId(workflowRunId);
            entity.setExecuteStatus("RUNNING");
            entity.setResult("");
            entity.setThinkProcess("");
            entity.setAnswerContent("");

            try {
                entity.setInputParams(objectMapper.writeValueAsString(inputs));
            } catch (Exception e) {
                entity.setInputParams(String.valueOf(inputs));
            }

            // 7. 根据组合主键查询是否已存在记录
            FinanceAnalysisEntity existingEntity = getOneByCompositeKey(analysisName, queryYear, compareYear, startMonth, endMonth);

            if (existingEntity == null) {
                success = save(entity);
            } else {
                entity.setId(existingEntity.getId());
                success = updateById(entity);
            }

            if (!success) {
                throw new RuntimeException("保存重点费用支出情况分析记录失败");
            }

            Long financeAnalysisId = entity.getId();

            // 8. 启动后台轮询任务：每10秒查询一次执行结果
            log.info("[重点费用支出情况分析-轮询] 启动轮询任务: financeAnalysisId={}, workflowRunId={}, period={}-{}, 间隔=10秒",
                    financeAnalysisId, workflowRunId, startMonth, endMonth);

            final String finalApiKey = financeAnalysisKey;
            final long financeAnalysisIdFinal = financeAnalysisId;
            final String workflowRunIdFinal = workflowRunId;
            final AtomicReference<ScheduledFuture<?>> futureRef = new AtomicReference<>();

            ScheduledFuture<?> scheduled = scheduler.scheduleAtFixedRate(() -> {
                try {
                    log.debug("[重点费用支出情况分析-轮询] tick: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                    FinanceAnalysisEntity current = getById(financeAnalysisIdFinal);
                    if (current == null || !"RUNNING".equalsIgnoreCase(current.getExecuteStatus())) {
                        // 已非运行状态，结束轮询
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null) {
                            f.cancel(false);
                        }
                        log.info("[重点费用支出情况分析-轮询] 停止轮询: financeAnalysisId={}, 当前状态={}", financeAnalysisIdFinal,
                                current == null ? "null" : current.getExecuteStatus());
                        return;
                    }

                    String detail = difyService.getWorkflowRunDetail(workflowRunIdFinal, finalApiKey);
                    if (detail == null || detail.isBlank()) {
                        log.warn("[重点费用支出情况分析-轮询] 未获取到工作流详情: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal,
                                workflowRunIdFinal);
                        return;
                    }

                    // 从返回内容中提取 status
                    String status = extractStatus(detail);
                    String lower = status == null ? null : status.toLowerCase();
                    log.info("[重点费用支出情况分析-轮询] 查询结果: financeAnalysisId={}, workflowRunId={}, status={}", financeAnalysisIdFinal,
                            workflowRunIdFinal, lower);

                    if ("succeeded".equals(lower) || "completed".equals(lower)) {
                        // 提取输出文本
                        String outputText = extractOutputText(detail);
                        current.setResult(outputText != null ? outputText : detail);
                        current.setExecuteStatus("COMPLETED");

                        // 解析Dify返回结果，提取think_process和answer_content
                        parseDifyWorkflowResult(current, outputText != null ? outputText : detail);

                        updateById(current);
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null) {
                            f.cancel(false);
                        }
                        log.info("[重点费用支出情况分析-轮询] 已完成: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                    } else if ("failed".equals(lower) || "error".equals(lower)) {
                        current.setResult("FAILED: " + detail);
                        current.setExecuteStatus("FAILED");
                        updateById(current);
                        ScheduledFuture<?> f = futureRef.get();
                        if (f != null) {
                            f.cancel(false);
                        }
                        log.info("[重点费用支出情况分析-轮询] 已失败: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                    }
                } catch (Exception ex) {
                    log.error("轮询Dify工作流结果异常, workflowRunId={}", workflowRunIdFinal, ex);
                }
            }, 10, 10, TimeUnit.SECONDS); // 延迟10秒执行
            futureRef.set(scheduled);

            return workflowRunId;

        } catch (Exception e) {
            log.error("分析重点费用支出情况失败", e);
            throw new RuntimeException("分析失败：" + e.getMessage(), e);
        }
    }



    /**
     * 分页查询财务分析数据
     *
     * @param page            分页参数
     * @param financeAnalysis 查询条件
     * @return 分页数据
     */
    @Override
    public IPage<FinanceAnalysisVO> selectFinanceAnalysisPage(IPage<FinanceAnalysisVO> page, FinanceAnalysisVO financeAnalysis) {
        IPage<FinanceAnalysisVO> pages = financeAnalysisMapper.selectFinanceAnalysisPage(page, financeAnalysis);
        return pages;
    }

    /**
     * 保存财务分析
     *
     * @param financeAnalysis 财务分析实体
     * @return 是否成功
     */
    @Override
    public boolean saveFinanceAnalysis(FinanceAnalysisEntity financeAnalysis) {
        return save(financeAnalysis);
    }

    /**
     * 更新财务分析
     *
     * @param financeAnalysis 财务分析实体
     * @return 是否成功
     */
    @Override
    public boolean updateFinanceAnalysis(FinanceAnalysisEntity financeAnalysis) {
        return updateById(financeAnalysis);
    }

    /**
     * 删除财务分析
     *
     * @param ids 主键集合
     * @return 是否成功
     */
    @Override
    public boolean removeFinanceAnalysis(String ids) {
        return removeByIds(Func.toLongList(ids));
    }

    /**
     * 获取财务分析详情
     *
     * @param id 主键
     * @return 财务分析详情
     */
    @Override
    public FinanceAnalysisVO getFinanceAnalysisDetail(Long id) {
        FinanceAnalysisEntity financeAnalysis = getById(id);
        return FinanceAnalysisWrapper.build().entityVO(financeAnalysis);
    }

    @Override
    public FinanceAnalysisEntity getOneByCompositeKey(String analysisName, Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) {
        LambdaQueryWrapper<FinanceAnalysisEntity> queryWrapper = new LambdaQueryWrapper<FinanceAnalysisEntity>()
                .eq(FinanceAnalysisEntity::getName, analysisName)
                .eq(FinanceAnalysisEntity::getQueryYear, queryYear)
                .eq(compareYear != null, FinanceAnalysisEntity::getCompareYear, compareYear)
                .eq(FinanceAnalysisEntity::getStartMonth, startMonth)
                .eq(FinanceAnalysisEntity::getEndMonth, endMonth);

        return getOne(queryWrapper);
    }


    @Override
    public FinanceAnalysisEntity getOneByWorkflowRunId(String workflowRunId) {
        LambdaQueryWrapper<FinanceAnalysisEntity> queryWrapper = new LambdaQueryWrapper<FinanceAnalysisEntity>()
                .eq(FinanceAnalysisEntity::getWorkflowRunId, workflowRunId);
        return getOne(queryWrapper);
    }

    @Override
    public List<FinanceAnalysisVO> listByCompositeKey(String name, Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) {
        LambdaQueryWrapper<FinanceAnalysisEntity> queryWrapper = new LambdaQueryWrapper<FinanceAnalysisEntity>();

        if (name != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getName, name);
        }

        if (queryYear != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getQueryYear, queryYear);
        }

        if (compareYear != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getCompareYear, compareYear);
        }

        if (startMonth != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getStartMonth, startMonth);
        }

        if (endMonth != null) {
            queryWrapper.eq(FinanceAnalysisEntity::getEndMonth, endMonth);
        }

        List<FinanceAnalysisEntity> entityList = list(queryWrapper);
        return entityList.stream()
                .map(entity -> FinanceAnalysisWrapper.build().entityVO(entity))
                .collect(Collectors.toList());
    }

    @Override
    public String analyzeMainEconomicIndicators(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth) {
        return analyzeMainEconomicIndicators(queryYear, compareYear, startMonth, endMonth, null);
    }

    @Override
    public String analyzeMainEconomicIndicators(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, Long reportId) {
        try {
            // 1. 获取主要经济指标表格数据
            Map<String, Object> params = new HashMap<>();
            params.put("queryYear", queryYear);
            params.put("compareYear", compareYear);
            params.put("startMonth", startMonth);
            params.put("endMonth", endMonth);

            List<Map<String, Object>> indicatorsData = selectMainEconomicIndicators(params);
            if (indicatorsData == null || indicatorsData.isEmpty()) {
                throw new RuntimeException("未获取到主要经济指标表格数据");
            }

            // 2. 创建或更新主要经济指标表格记录
            FinanceAnalysisEntity entity = new FinanceAnalysisEntity();
            entity.setName("主要经济指标表格");
            entity.setType("economic_indicators");
            entity.setQueryYear(queryYear);
            entity.setCompareYear(compareYear);
            entity.setStartMonth(startMonth);
            entity.setEndMonth(endMonth);
            entity.setReportId(reportId); // 设置报告ID
            entity.setExecuteStatus("COMPLETED");
            entity.setResult(objectMapper.writeValueAsString(indicatorsData));
            boolean success = saveOrUpdate("主要经济指标表格", queryYear, compareYear, startMonth, endMonth, entity);
            if (!success) {
                throw new RuntimeException("保存主要经济指标表格记录失败");
            }

            // 3. 实现税利情况分析
            String taxProfitWorkflowRunId = analyzeTaxAndProfit(queryYear, compareYear, startMonth, endMonth, indicatorsData, reportId);

            // 4. 卷烟经营情况分析
            String cigaretteWorkflowRunId = analyzeCigaretteOperation(queryYear, compareYear, startMonth, endMonth, indicatorsData, reportId);
            return taxProfitWorkflowRunId + "," + cigaretteWorkflowRunId;
        } catch (Exception e) {
            log.error("分析财务分析失败", e);
            throw new RuntimeException("分析失败：" + e.getMessage(), e);
        }
    }

    @Override
    public String analyzeTaxAndProfit(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, List<Map<String, Object>> indicatorsData) throws JsonProcessingException {
        return analyzeTaxAndProfit(queryYear, compareYear, startMonth, endMonth, null);
    }

    /**
     * 实现税利情况分析
     * @param queryYear
     * @param compareYear
     * @param startMonth
     * @param endMonth
     * @param indicatorsData
     * @return
     * @throws JsonProcessingException
     */
    private String analyzeTaxAndProfit(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, List<Map<String, Object>> indicatorsData, Long reportId) throws JsonProcessingException {
        // 1. 计算各项指标值
        Map<String, Object> calculatedValues = calculateTaxProfitIndicatorValues(indicatorsData, queryYear, compareYear, startMonth, endMonth);

        // 2. 组装输入参数
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("dataList", objectMapper.writeValueAsString(indicatorsData));
        inputs.put("calculatedValues", objectMapper.writeValueAsString(calculatedValues));
        inputs.put("queryYear", queryYear);
        inputs.put("compareYear", compareYear);
        inputs.put("startMonth", startMonth);
        inputs.put("endMonth", endMonth);
        inputs.put("analysisType", "实现税利情况分析");

        String userName = AuthUtil.getUserName();
        // 3. 启动Dify工作流
        String workflowRunId = difyService.startWorkflowStreaming(inputs, StringUtil.isNotBlank(userName) ? userName : "yjzb", financeAnalysisKey);

        if (workflowRunId == null) {
            log.error("启动Dify工作流失败，请检查 Dify 配置与权限");
            throw new RuntimeException("启动Dify工作流失败");
        }

        log.info("启动实现税利情况分析AI分析：queryYear={}, compareYear={}, period={}-{}, workflowRunId={}",
                queryYear, compareYear, startMonth, endMonth, workflowRunId);

        // 4. 创建或更新财务分析记录
        FinanceAnalysisEntity entity = new FinanceAnalysisEntity();
        entity.setName("实现税利情况分析");
        entity.setType("tax_profit_analysis");
        entity.setQueryYear(queryYear);
        entity.setCompareYear(compareYear);
        entity.setStartMonth(startMonth);
        entity.setEndMonth(endMonth);
        try {
            entity.setInputParams(objectMapper.writeValueAsString(inputs));
        } catch (Exception e) {
            entity.setInputParams(String.valueOf(inputs));
        }
        entity.setWorkflowRunId(workflowRunId);
        entity.setReportId(reportId); // 设置报告ID
        entity.setExecuteStatus("RUNNING");
        entity.setResult("");
        entity.setThinkProcess("");
        entity.setAnswerContent("");

        boolean success = saveOrUpdate("实现税利情况分析", queryYear, compareYear, startMonth, endMonth, entity);
        if (!success) {
            throw new RuntimeException("保存实现税利情况分析记录失败");
        }

        Long financeAnalysisId = entity.getId();

        // 5. 启动后台轮询任务：每3分钟查询一次执行结果
        log.info("[实现税利情况分析-轮询] 启动轮询任务: financeAnalysisId={}, workflowRunId={}, period={}-{}, 间隔=10秒",
                financeAnalysisId, workflowRunId, startMonth, endMonth);

        final String finalApiKey = financeAnalysisKey;
        final long financeAnalysisIdFinal = financeAnalysisId;
        final String workflowRunIdFinal = workflowRunId;
        final AtomicReference<ScheduledFuture<?>> futureRef = new AtomicReference<>();

        ScheduledFuture<?> scheduled = scheduler.scheduleAtFixedRate(() -> {
            try {
                log.debug("[实现税利情况分析-轮询] tick: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                FinanceAnalysisEntity current = getById(financeAnalysisIdFinal);
                if (current == null || !"RUNNING".equalsIgnoreCase(current.getExecuteStatus())) {
                    // 已非运行状态，结束轮询
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[实现税利情况分析-轮询] 停止轮询: financeAnalysisId={}, 当前状态={}", financeAnalysisIdFinal,
                            current == null ? "null" : current.getExecuteStatus());
                    return;
                }

                String detail = difyService.getWorkflowRunDetail(workflowRunIdFinal, finalApiKey);
                if (detail == null || detail.isBlank()) {
                    log.warn("[实现税利情况分析-轮询] 未获取到工作流详情: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal,
                            workflowRunIdFinal);
                    return;
                }

                // 从返回内容中提取 status
                String status = extractStatus(detail);
                String lower = status == null ? null : status.toLowerCase();
                log.info("[实现税利情况分析-轮询] 查询结果: financeAnalysisId={}, workflowRunId={}, status={}", financeAnalysisIdFinal,
                        workflowRunIdFinal, lower);

                if ("succeeded".equals(lower) || "completed".equals(lower)) {
                    // 提取输出文本
                    String outputText = extractOutputText(detail);
                    current.setExecuteStatus("COMPLETED");
                    current.setResult(outputText != null ? outputText : detail);

                    // 解析Dify返回结果，提取think_process和answer_content
                    parseDifyWorkflowResult(current, outputText != null ? outputText : detail);

                    updateById(current);
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[实现税利情况分析-轮询] 已完成: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                } else if ("failed".equals(lower) || "error".equals(lower)) {
                    current.setExecuteStatus("FAILED");
                    current.setResult(detail);
                    updateById(current);
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[实现税利情况分析-轮询] 已失败: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                }
            } catch (Exception ex) {
                log.error("轮询Dify工作流结果异常, workflowRunId={}", workflowRunIdFinal, ex);
            }
        }, 10, 10, TimeUnit.SECONDS); // 延迟10秒执行
        futureRef.set(scheduled);
        return workflowRunId;
    }

    private boolean saveOrUpdate(String analysisName, Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, FinanceAnalysisEntity entity) {
        // 6. 根据组合主键查询是否已存在记录
        FinanceAnalysisEntity existingEntity = getOneByCompositeKey(analysisName, queryYear, compareYear, startMonth, endMonth);

        boolean success;
        if (existingEntity == null) {
            success = save(entity);
        } else {
            entity.setId(existingEntity.getId());
            success = updateById(entity);
        }
        return success;
    }

    public FinanceAnalysisVO updateAnalysisResultByWorkflowRunId(String workflowRunId) {
        FinanceAnalysisEntity entity = this.getOneByWorkflowRunId(workflowRunId);

        if (null == entity) {
            return null;
        }

        // 如果结果为空或者是原始数据，尝试重新获取工作流执行详情
        if (entity.getResult() == null || entity.getResult().contains("indicators")) {
            String workflowDetail = difyService.getWorkflowRunDetail(workflowRunId, financeAnalysisKey);
            if (workflowDetail != null) {
                entity.setResult(workflowDetail);

                // 解析Dify返回结果，提取think_process和answer_content
                parseDifyWorkflowResult(entity, workflowDetail);

                this.updateFinanceAnalysis(entity);
            }
        }

        return FinanceAnalysisWrapper.build().entityVO(entity);
    }

    /**
     * 计算实现税利情况分析各项指标值
     */
    private Map<String, Object> calculateTaxProfitIndicatorValues(List<Map<String, Object>> indicatorsData,
                                                                  Integer queryYear, Integer compareYear,
                                                                  Integer startMonth, Integer endMonth) {
        Map<String, Object> result = new HashMap<>();

        if (indicatorsData == null || indicatorsData.isEmpty()) {
            return result;
        }

        // 从指标数据中提取各项值
        Map<String, Object> taxProfitData = findIndicatorByName(indicatorsData, "税利总额");
        Map<String, Object> profitData = findIndicatorByName(indicatorsData, "利润总额");
        Map<String, Object> taxData = findIndicatorByName(indicatorsData, "税费（不含所得税）");

        if (taxProfitData != null) {
            // 税利总额本年值
            BigDecimal currentValue = getBigDecimalValue(taxProfitData, "currentValue");
            BigDecimal lastYearValue = getBigDecimalValue(taxProfitData, "lastYearValue");
            BigDecimal budgetValue = getBigDecimalValue(taxProfitData, "budgetValue");
            BigDecimal executionRate = getBigDecimalValue(taxProfitData, "executionRate");

            result.put("税利总额本年值", currentValue != null ? currentValue.intValue() : 0);

            // 税利同比增加额
            if (currentValue != null && lastYearValue != null) {
                BigDecimal increaseAmount = currentValue.subtract(lastYearValue);
                result.put("税利同比增加额", increaseAmount.intValue());
            } else {
                result.put("税利同比增加额", 0);
            }

            // 税利同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("税利同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("税利同比增幅", BigDecimal.ZERO);
            }

            // 全年税利预算数
            result.put("全年税利预算数", budgetValue != null ? budgetValue.intValue() : 0);

            // 税利预算执行进度
            result.put("税利预算执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        }

        if (profitData != null) {
            // 利润总额本年值
            BigDecimal currentValue = getBigDecimalValue(profitData, "currentValue");
            BigDecimal lastYearValue = getBigDecimalValue(profitData, "lastYearValue");

            result.put("利润总额本年值", currentValue != null ? currentValue.intValue() : 0);

            // 利润总额同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("利润总额同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("利润总额同比增幅", BigDecimal.ZERO);
            }
        }

        if (taxData != null) {
            // 税费本年值
            BigDecimal currentValue = getBigDecimalValue(taxData, "currentValue");
            BigDecimal lastYearValue = getBigDecimalValue(taxData, "lastYearValue");

            result.put("税费本年值", currentValue != null ? currentValue.intValue() : 0);

            // 税费同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("税费同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("税费同比增幅", BigDecimal.ZERO);
            }
        }

        // 当前时期末
        result.put("当前时期末", endMonth);

        // 当前时期末最后一天
        int lastDay = getLastDayOfMonth(queryYear, endMonth);
        result.put("当前时期末最后一天", lastDay);

        // 时间进度要求 (月份/12*100，保留两位小数)
        BigDecimal timeProgress = new BigDecimal(endMonth)
                .divide(new BigDecimal("12"), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
        result.put("时间进度要求", timeProgress.setScale(2, RoundingMode.HALF_UP));

        // 进度差额
        BigDecimal executionRate = (BigDecimal) result.get("税利预算执行进度");
        if (executionRate != null) {
            BigDecimal progressDiff = executionRate.subtract(timeProgress);
            result.put("进度差额", progressDiff.setScale(2, RoundingMode.HALF_UP));
        } else {
            result.put("进度差额", BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 根据指标名称查找指标数据
     */
    private Map<String, Object> findIndicatorByName(List<Map<String, Object>> indicatorsData, String indicatorName) {
        if (indicatorsData == null || indicatorName == null) {
            return null;
        }

        return indicatorsData.stream()
                .filter(data -> indicatorName.equals(data.get("项目")) || indicatorName.equals(data.get("indicatorName")))
                .findFirst()
                .orElse(null);
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> data, String key) {
        if (data == null || key == null) {
            return null;
        }

        Object value = data.get(key);
        if (value == null) {
            return null;
        }

        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        } else if (value instanceof Number) {
            return new BigDecimal(value.toString());
        } else if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }

        return null;
    }

    /**
     * 获取指定年月的最后一天
     */
    private int getLastDayOfMonth(int year, int month) {
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                return 31;
            case 4:
            case 6:
            case 9:
            case 11:
                return 30;
            case 2:
                // 判断闰年
                if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
                    return 29;
                } else {
                    return 28;
                }
            default:
                return 31;
        }
    }

    /**
     * 提取工作流状态字段，兼容以下返回结构：
     * 1) { status: "succeeded" }
     * 2) { data: { status: "succeeded" } }
     * 3) { data: "{\"status\":\"succeeded\", ... }" }
     */
    private String extractStatus(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;
            // 直接在根查找
            if (node.has("status")) {
                return node.get("status").asText();
            }
            // 在 data 节点查找
            if (node.has("data")) {
                com.fasterxml.jackson.databind.JsonNode data = node.get("data");
                if (data.isObject() && data.has("status")) {
                    return data.get("status").asText();
                }
                if (data.isTextual()) {
                    // data 是一个 JSON 字符串，再次反序列化
                    String dataText = data.asText();
                    com.fasterxml.jackson.databind.JsonNode dataObj = objectMapper.readTree(dataText);
                    if (dataObj != null && dataObj.has("status")) {
                        return dataObj.get("status").asText();
                    }
                }
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    /**
     * 提取 outputs.text 内容；若 outputs 为字符串JSON，会先反序列化再取 text
     */
    private String extractOutputText(String detailJson) {
        try {
            com.fasterxml.jackson.databind.JsonNode node = objectMapper.readTree(detailJson);
            if (node == null)
                return null;
            com.fasterxml.jackson.databind.JsonNode outputs = node.get("outputs");
            if (outputs == null)
                return null;
            if (outputs.isObject()) {
                com.fasterxml.jackson.databind.JsonNode text = outputs.get("text");
                return text != null ? text.asText() : outputs.toString();
            }
            if (outputs.isTextual()) {
                String outputsText = outputs.asText();
                com.fasterxml.jackson.databind.JsonNode outObj = objectMapper.readTree(outputsText);
                if (outObj != null && outObj.has("text")) {
                    return outObj.get("text").asText();
                }
                return outputsText;
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    @Override
    public String analyzeCigaretteOperation(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, List<Map<String, Object>> indicatorsData) throws JsonProcessingException {
        return analyzeCigaretteOperation(queryYear, compareYear, startMonth, endMonth, null);
    }

    /**
     * 卷烟经营情况分析
     */
    private String analyzeCigaretteOperation(Integer queryYear, Integer compareYear, Integer startMonth, Integer endMonth, List<Map<String, Object>> indicatorsData, Long reportId) throws JsonProcessingException {
        // 1. 计算各项指标值
        Map<String, Object> calculatedValues = calculateCigaretteOperationIndicatorValues(indicatorsData, queryYear, compareYear, startMonth, endMonth);

        // 2. 组装输入参数
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("dataList", objectMapper.writeValueAsString(indicatorsData));
        inputs.put("calculatedValues", objectMapper.writeValueAsString(calculatedValues));
        inputs.put("queryYear", queryYear);
        inputs.put("compareYear", compareYear);
        inputs.put("startMonth", startMonth);
        inputs.put("endMonth", endMonth);
        inputs.put("analysisType", "卷烟经营情况分析");

        String userName = AuthUtil.getUserName();
        // 3. 启动Dify工作流
        String workflowRunId = difyService.startWorkflowStreaming(inputs, StringUtil.isNotBlank(userName) ? userName : "yjzb", financeAnalysisKey);

        if (workflowRunId == null) {
            log.error("启动Dify工作流失败，请检查 Dify 配置与权限");
            throw new RuntimeException("启动Dify工作流失败");
        }

        log.info("启动卷烟经营情况分析AI分析：queryYear={}, compareYear={}, period={}-{}, workflowRunId={}",
                queryYear, compareYear, startMonth, endMonth, workflowRunId);

        // 4. 创建或更新财务分析记录
        FinanceAnalysisEntity entity = new FinanceAnalysisEntity();
        entity.setName("卷烟经营情况分析");
        entity.setType("cigarette_operation_analysis");
        entity.setQueryYear(queryYear);
        entity.setCompareYear(compareYear);
        entity.setStartMonth(startMonth);
        entity.setEndMonth(endMonth);
        try {
            entity.setInputParams(objectMapper.writeValueAsString(inputs));
        } catch (Exception e) {
            entity.setInputParams(String.valueOf(inputs));
        }
        entity.setWorkflowRunId(workflowRunId);
        entity.setReportId(reportId); // 设置报告ID
        entity.setExecuteStatus("RUNNING");
        entity.setResult("");
        entity.setThinkProcess("");
        entity.setAnswerContent("");
        boolean success = saveOrUpdate("卷烟经营情况分析", queryYear, compareYear, startMonth, endMonth, entity);
        if (!success) {
            throw new RuntimeException("保存卷烟经营情况分析记录失败");
        }

        Long financeAnalysisId = entity.getId();

        // 5. 启动后台轮询任务：每10秒查询一次执行结果
        log.info("[卷烟经营情况分析-轮询] 启动轮询任务: financeAnalysisId={}, workflowRunId={}, period={}-{}, 间隔=10秒",
                financeAnalysisId, workflowRunId, startMonth, endMonth);

        final String finalApiKey = financeAnalysisKey;
        final long financeAnalysisIdFinal = financeAnalysisId;
        final String workflowRunIdFinal = workflowRunId;
        final AtomicReference<ScheduledFuture<?>> futureRef = new AtomicReference<>();

        ScheduledFuture<?> scheduled = scheduler.scheduleAtFixedRate(() -> {
            try {
                log.debug("[卷烟经营情况分析-轮询] tick: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                FinanceAnalysisEntity current = getById(financeAnalysisIdFinal);
                if (current == null || !"RUNNING".equalsIgnoreCase(current.getExecuteStatus())) {
                    // 已非运行状态，结束轮询
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[卷烟经营情况分析-轮询] 停止轮询: financeAnalysisId={}, 当前状态={}", financeAnalysisIdFinal,
                            current == null ? "null" : current.getExecuteStatus());
                    return;
                }

                String detail = difyService.getWorkflowRunDetail(workflowRunIdFinal, finalApiKey);
                if (detail == null || detail.isBlank()) {
                    log.warn("[卷烟经营情况分析-轮询] 未获取到工作流详情: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal,
                            workflowRunIdFinal);
                    return;
                }

                // 从返回内容中提取 status
                String status = extractStatus(detail);
                String lower = status == null ? null : status.toLowerCase();
                log.info("[卷烟经营情况分析-轮询] 查询结果: financeAnalysisId={}, workflowRunId={}, status={}", financeAnalysisIdFinal,
                        workflowRunIdFinal, lower);

                if ("succeeded".equals(lower) || "completed".equals(lower)) {
                    // 提取输出文本
                    String outputText = extractOutputText(detail);
                    current.setResult(outputText != null ? outputText : detail);
                    current.setExecuteStatus("COMPLETED");

                    // 解析Dify返回结果，提取think_process和answer_content
                    parseDifyWorkflowResult(current, outputText != null ? outputText : detail);

                    updateById(current);
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[卷烟经营情况分析-轮询] 已完成: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                } else if ("failed".equals(lower) || "error".equals(lower)) {
                    current.setResult("FAILED: " + detail);
                    current.setExecuteStatus("FAILED");
                    updateById(current);
                    ScheduledFuture<?> f = futureRef.get();
                    if (f != null) {
                        f.cancel(false);
                    }
                    log.info("[卷烟经营情况分析-轮询] 已失败: financeAnalysisId={}, workflowRunId={}", financeAnalysisIdFinal, workflowRunIdFinal);
                }
            } catch (Exception ex) {
                log.error("轮询Dify工作流结果异常, workflowRunId={}", workflowRunIdFinal, ex);
            }
        }, 10, 10, TimeUnit.SECONDS); // 延迟10秒执行
        futureRef.set(scheduled);

        return workflowRunId;
    }

    /**
     * 计算卷烟经营情况分析各项指标值
     */
    private Map<String, Object> calculateCigaretteOperationIndicatorValues(List<Map<String, Object>> indicatorsData,
                                                                           Integer queryYear, Integer compareYear,
                                                                           Integer startMonth, Integer endMonth) {
        Map<String, Object> result = new HashMap<>();

        if (indicatorsData == null || indicatorsData.isEmpty()) {
            return result;
        }

        // 从指标数据中提取各项值
        Map<String, Object> salesRevenueData = findIndicatorByName(indicatorsData, "卷烟销售收入");
        Map<String, Object> salesQuantityData = findIndicatorByName(indicatorsData, "卷烟销售数量");
        Map<String, Object> grossProfitData = findIndicatorByName(indicatorsData, "毛利额");
        Map<String, Object> grossProfitRateData = findIndicatorByName(indicatorsData, "毛利率（%）");

        // 当前时期
        String currentPeriod = queryYear + "年1-" + endMonth + "月";
        result.put("当前时期", currentPeriod);

        // 卷烟销售收入和卷烟销量分析
        if (salesRevenueData != null) {
            BigDecimal currentValue = getBigDecimalValue(salesRevenueData, "currentValue");
            BigDecimal lastYearValue = getBigDecimalValue(salesRevenueData, "lastYearValue");

            result.put("卷烟销售收入本年值", currentValue != null ? currentValue.intValue() : 0);

            // 卷烟销售收入同比增加额
            if (currentValue != null && lastYearValue != null) {
                BigDecimal increaseAmount = currentValue.subtract(lastYearValue);
                result.put("卷烟销售收入同比增加额", increaseAmount.intValue());
            } else {
                result.put("卷烟销售收入同比增加额", 0);
            }

            // 卷烟销售收入同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("卷烟销售收入同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("卷烟销售收入同比增幅", BigDecimal.ZERO);
            }
        }

        if (salesQuantityData != null) {
            BigDecimal currentValue = getBigDecimalValue(salesQuantityData, "currentValue");
            BigDecimal lastYearValue = getBigDecimalValue(salesQuantityData, "lastYearValue");

            result.put("卷烟销售数量本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

            // 卷烟销售数量同比增加额
            if (currentValue != null && lastYearValue != null) {
                BigDecimal increaseAmount = currentValue.subtract(lastYearValue);
                result.put("卷烟销售数量同比增加额", increaseAmount.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("卷烟销售数量同比增加额", BigDecimal.ZERO);
            }

            // 卷烟销售数量同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("卷烟销售数量同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("卷烟销售数量同比增幅", BigDecimal.ZERO);
            }
        }

        // 卷烟结构分析 - 单箱收入计算
        BigDecimal salesRevenueCurrent = getBigDecimalValue(salesRevenueData, "currentValue");
        BigDecimal salesQuantityCurrent = getBigDecimalValue(salesQuantityData, "currentValue");
        BigDecimal salesRevenueLastYear = getBigDecimalValue(salesRevenueData, "lastYearValue");
        BigDecimal salesQuantityLastYear = getBigDecimalValue(salesQuantityData, "lastYearValue");
        BigDecimal salesRevenueBudget = getBigDecimalValue(salesRevenueData, "budgetValue");
        BigDecimal salesQuantityBudget = getBigDecimalValue(salesQuantityData, "budgetValue");

        if (salesRevenueCurrent != null && salesQuantityCurrent != null && salesQuantityCurrent.compareTo(BigDecimal.ZERO) != 0) {
            // 单箱含税收入计算值 (假设增值税率13%)
            BigDecimal unitRevenueWithTax = salesRevenueCurrent
                    .multiply(new BigDecimal("1.13"))
                    .multiply(new BigDecimal("10000"))
                    .divide(salesQuantityCurrent, 0, RoundingMode.HALF_UP);
            result.put("单箱含税收入计算值", unitRevenueWithTax.intValue());

            // 计算上年单箱收入
            if (salesRevenueLastYear != null && salesQuantityLastYear != null && salesQuantityLastYear.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal lastYearUnitRevenueWithTax = salesRevenueLastYear
                        .multiply(new BigDecimal("1.13"))
                        .multiply(new BigDecimal("10000"))
                        .divide(salesQuantityLastYear, 0, RoundingMode.HALF_UP);

                // 单箱收入同比增加额
                BigDecimal unitRevenueIncrease = unitRevenueWithTax.subtract(lastYearUnitRevenueWithTax);
                result.put("单箱收入同比增加额", unitRevenueIncrease.intValue());

                // 单箱收入同比增幅
                if (lastYearUnitRevenueWithTax.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal unitRevenueGrowthRate = unitRevenueIncrease
                            .divide(lastYearUnitRevenueWithTax, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"));
                    result.put("单箱收入同比增幅", unitRevenueGrowthRate.setScale(2, RoundingMode.HALF_UP));
                } else {
                    result.put("单箱收入同比增幅", BigDecimal.ZERO);
                }
            }

            // 年度单箱收入预算值
            if (salesRevenueBudget != null && salesQuantityBudget != null && salesQuantityBudget.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal budgetUnitRevenueWithTax = salesRevenueBudget
                        .multiply(new BigDecimal("1.13"))
                        .multiply(new BigDecimal("10000"))
                        .divide(salesQuantityBudget, 0, RoundingMode.HALF_UP);
                result.put("年度单箱收入预算值", budgetUnitRevenueWithTax.intValue());

                // 单箱收入超预算额
                BigDecimal unitRevenueExceedBudget = unitRevenueWithTax.subtract(budgetUnitRevenueWithTax);
                result.put("单箱收入超预算额", unitRevenueExceedBudget.intValue());
            }
        }

        // 卷烟销售毛利和毛利率分析
        if (grossProfitData != null) {
            BigDecimal currentValue = getBigDecimalValue(grossProfitData, "currentValue");
            BigDecimal lastYearValue = getBigDecimalValue(grossProfitData, "lastYearValue");

            result.put("毛利额本年值", currentValue != null ? currentValue.intValue() : 0);

            // 毛利额同比增加额
            if (currentValue != null && lastYearValue != null) {
                BigDecimal increaseAmount = currentValue.subtract(lastYearValue);
                result.put("毛利额同比增加额", increaseAmount.intValue());
            } else {
                result.put("毛利额同比增加额", 0);
            }

            // 毛利额同比增幅
            if (currentValue != null && lastYearValue != null && lastYearValue.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal growthRate = currentValue.subtract(lastYearValue)
                        .divide(lastYearValue, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                result.put("毛利额同比增幅", growthRate.setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("毛利额同比增幅", BigDecimal.ZERO);
            }
        }

        if (grossProfitRateData != null) {
            BigDecimal currentValue = getBigDecimalValue(grossProfitRateData, "currentValue");
            BigDecimal lastYearValue = getBigDecimalValue(grossProfitRateData, "lastYearValue");
            BigDecimal budgetValue = getBigDecimalValue(grossProfitRateData, "budgetValue");

            result.put("毛利率本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

            // 毛利率同比变化
            if (currentValue != null && lastYearValue != null) {
                BigDecimal changeAmount = currentValue.subtract(lastYearValue);
                result.put("毛利率同比变化绝对值", changeAmount.abs().setScale(2, RoundingMode.HALF_UP));

                if (changeAmount.compareTo(BigDecimal.ZERO) > 0) {
                    result.put("毛利率同比变化方向", "提高");
                } else if (changeAmount.compareTo(BigDecimal.ZERO) < 0) {
                    result.put("毛利率同比变化方向", "下降");
                } else {
                    result.put("毛利率同比变化方向", "持平");
                }
            } else {
                result.put("毛利率同比变化绝对值", BigDecimal.ZERO);
                result.put("毛利率同比变化方向", "持平");
            }

            // 与预算比较情况描述
            if (currentValue != null && budgetValue != null) {
                BigDecimal budgetDiff = currentValue.subtract(budgetValue);
                if (budgetDiff.compareTo(BigDecimal.ZERO) > 0) {
                    result.put("与预算比较情况描述", "超过毛利率预算数" + budgetValue.setScale(2, RoundingMode.HALF_UP) + "%");
                } else if (budgetDiff.compareTo(BigDecimal.ZERO) < 0) {
                    result.put("与预算比较情况描述", "低于预算" + budgetDiff.abs().setScale(2, RoundingMode.HALF_UP) + "个百分点");
                } else {
                    result.put("与预算比较情况描述", "与预算持平");
                }
            } else {
                result.put("与预算比较情况描述", "预算数据不可用");
            }
        }

        return result;
    }

    /**
     * 计算三项费用支出总体情况分析各项指标值
     */
    private Map<String, Object> calculateThreeExpensesIndicatorValues(List<Map<String, Object>> threeExpensesData,
                                                                      Integer queryYear, Integer compareYear,
                                                                      Integer startMonth, Integer endMonth) {
        Map<String, Object> result = new HashMap<>();

        if (threeExpensesData == null || threeExpensesData.isEmpty()) {
            return result;
        }

        // 从三项费用数据中提取合计行数据
        Map<String, Object> totalData = threeExpensesData.stream()
                .filter(data -> "合计".equals(data.get("预算项目")))
                .findFirst()
                .orElse(null);

        // 当前时期
        String currentPeriod = queryYear + "年1-" + endMonth + "月";
        result.put("当前时期", currentPeriod);

        if (totalData != null) {
            // 三项费用支出合计本年值
            BigDecimal currentValue = getBigDecimalValue(totalData, "currentValue");
            BigDecimal lastYearValue = getBigDecimalValue(totalData, "lastYearValue");
            BigDecimal diffAmount = getBigDecimalValue(totalData, "diffAmount");
            BigDecimal diffPercent = getBigDecimalValue(totalData, "diffPercent");

            result.put("三项费用支出合计本年值", currentValue != null ? currentValue.intValue() : 0);
            result.put("三项费用支出合计上年值", lastYearValue != null ? lastYearValue.intValue() : 0);

            // 三项费用同比增减额
            if (diffAmount != null) {
                result.put("三项费用同比增减额", diffAmount.intValue());
                // 判断是增加还是减少
                if (diffAmount.compareTo(BigDecimal.ZERO) >= 0) {
                    result.put("三项费用同比变化方向", "增加");
                } else {
                    result.put("三项费用同比变化方向", "减少");
                    result.put("三项费用同比增减额", diffAmount.abs().intValue()); // 取绝对值
                }
            } else {
                result.put("三项费用同比增减额", 0);
                result.put("三项费用同比变化方向", "持平");
            }

            // 三项费用同比增减幅
            if (diffPercent != null) {
                result.put("三项费用同比增减幅", diffPercent.abs().setScale(2, RoundingMode.HALF_UP));
            } else {
                result.put("三项费用同比增减幅", BigDecimal.ZERO);
            }
        }

        // 提取销售费用、管理费用、财务费用的具体数据
        Map<String, Object> salesExpenseData = findExpenseByName(threeExpensesData, "销售费用");
        Map<String, Object> managementExpenseData = findExpenseByName(threeExpensesData, "管理费用");
        Map<String, Object> financialExpenseData = findExpenseByName(threeExpensesData, "财务费用");

        if (salesExpenseData != null) {
            BigDecimal salesCurrent = getBigDecimalValue(salesExpenseData, "currentValue");
            result.put("销售费用本年值", salesCurrent != null ? salesCurrent.intValue() : 0);
        }

        if (managementExpenseData != null) {
            BigDecimal managementCurrent = getBigDecimalValue(managementExpenseData, "currentValue");
            result.put("管理费用本年值", managementCurrent != null ? managementCurrent.intValue() : 0);
        }

        if (financialExpenseData != null) {
            BigDecimal financialCurrent = getBigDecimalValue(financialExpenseData, "currentValue");
            result.put("财务费用本年值", financialCurrent != null ? financialCurrent.intValue() : 0);
        }

        // 计算三项费用率
        // 需要从主要经济指标中获取主营业务收入数据来计算费用率
        Map<String, Object> params = new HashMap<>();
        params.put("queryYear", queryYear);
        params.put("compareYear", compareYear);
        params.put("startMonth", startMonth);
        params.put("endMonth", endMonth);

        try {
            List<Map<String, Object>> mainIndicators = selectMainEconomicIndicators(params);
            Map<String, Object> revenueData = findIndicatorByName(mainIndicators, "卷烟销售收入");

            if (revenueData != null && totalData != null) {
                BigDecimal revenue = getBigDecimalValue(revenueData, "currentValue");
                BigDecimal lastYearRevenue = getBigDecimalValue(revenueData, "lastYearValue");
                BigDecimal totalExpense = getBigDecimalValue(totalData, "currentValue");
                BigDecimal lastYearTotalExpense = getBigDecimalValue(totalData, "lastYearValue");

                if (revenue != null && totalExpense != null && revenue.compareTo(BigDecimal.ZERO) != 0) {
                    // 三项费用率（当年）
                    BigDecimal expenseRate = totalExpense.multiply(new BigDecimal("100"))
                            .divide(revenue, 2, RoundingMode.HALF_UP);
                    result.put("三项费用率", expenseRate);

                    // 三项费用率（上年）
                    if (lastYearRevenue != null && lastYearTotalExpense != null && lastYearRevenue.compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal lastYearExpenseRate = lastYearTotalExpense.multiply(new BigDecimal("100"))
                                .divide(lastYearRevenue, 2, RoundingMode.HALF_UP);
                        result.put("上年三项费用率", lastYearExpenseRate);

                        // 三项费用率同比变化
                        BigDecimal rateChange = expenseRate.subtract(lastYearExpenseRate);
                        result.put("三项费用率同比变化", rateChange.setScale(2, RoundingMode.HALF_UP));

                        if (rateChange.compareTo(BigDecimal.ZERO) > 0) {
                            result.put("三项费用率变化方向", "增长");
                        } else if (rateChange.compareTo(BigDecimal.ZERO) < 0) {
                            result.put("三项费用率变化方向", "下降");
                        } else {
                            result.put("三项费用率变化方向", "持平");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("计算三项费用率时出错", e);
            result.put("三项费用率", BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 根据费用名称查找费用数据
     */
    private Map<String, Object> findExpenseByName(List<Map<String, Object>> expensesData, String expenseName) {
        if (expensesData == null || expenseName == null) {
            return null;
        }

        return expensesData.stream()
                .filter(data -> expenseName.equals(data.get("预算项目")))
                .findFirst()
                .orElse(null);
    }

    /**
     * 计算重点费用支出情况分析各项指标值
     */
    private Map<String, Object> calculateKeyExpensesIndicatorValues(List<Map<String, Object>> keyExpensesData,
                                                                    Integer queryYear, Integer compareYear,
                                                                    Integer startMonth, Integer endMonth) {
        Map<String, Object> result = new HashMap<>();

        if (keyExpensesData == null || keyExpensesData.isEmpty()) {
            return result;
        }

        // 当前时期
        String currentPeriod = queryYear + "年1-" + endMonth + "月";
        result.put("当前时期", currentPeriod);

        // 截至日期
        int lastDay = getLastDayOfMonth(queryYear, endMonth);
        String deadlineDate = queryYear + "年" + endMonth + "月" + lastDay + "日";
        result.put("截至日期", deadlineDate);

        // 从重点费用数据中提取各项指标
        for (Map<String, Object> expenseData : keyExpensesData) {
            String itemName = (String) expenseData.get("项目");
            if (itemName == null) continue;

            BigDecimal currentValue = getBigDecimalValue(expenseData, "currentValue");
            BigDecimal lastYearValue = getBigDecimalValue(expenseData, "lastYearValue");
            BigDecimal budgetValue = getBigDecimalValue(expenseData, "budgetValue");
            BigDecimal executionRate = getBigDecimalValue(expenseData, "executionRate");
            BigDecimal diffPercent = getBigDecimalValue(expenseData, "diffPercent");

            // 根据项目名称设置对应的变量
            switch (itemName) {
                case "两项费用率（%）":
                    result.put("两项费用率本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("两项费用率上年值", lastYearValue != null ? lastYearValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    if (currentValue != null && lastYearValue != null) {
                        BigDecimal change = currentValue.subtract(lastYearValue);
                        result.put("两项费用率同比变化", change.setScale(2, RoundingMode.HALF_UP));
                        result.put("两项费用率变化方向", change.compareTo(BigDecimal.ZERO) >= 0 ? "增长" : "下降");
                    }
                    break;

                case "会议费":
                    result.put("会议费本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("会议费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("会议费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("会议费同比增幅", diffPercent != null ? diffPercent.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "车辆运行费":
                    result.put("车辆运行费本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("车辆运行费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("车辆运行费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "福利费":
                    result.put("福利费本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("福利费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("福利费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "实发工资":
                    result.put("实发工资本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("实发工资预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("实发工资执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    if (currentValue != null && lastYearValue != null) {
                        BigDecimal change = currentValue.subtract(lastYearValue);
                        result.put("实发工资同比变化额", change.setScale(2, RoundingMode.HALF_UP));
                        result.put("实发工资同比变化方向", change.compareTo(BigDecimal.ZERO) >= 0 ? "增加" : "减少");
                    }
                    result.put("实发工资同比变化幅度", diffPercent != null ? diffPercent.abs().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "零售终端建设费":
                    result.put("零售终端建设费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("零售终端建设费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;

                case "研发费":
                    result.put("研发费本年值", currentValue != null ? currentValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("研发费预算值", budgetValue != null ? budgetValue.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    result.put("研发费执行进度", executionRate != null ? executionRate.setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    break;
            }
        }

        // 计算一些特殊的指标
        // 福利费占实发工资比例
        BigDecimal welfareAmount = (BigDecimal) result.get("福利费本年值");
        BigDecimal salaryAmount = (BigDecimal) result.get("实发工资本年值");
        if (welfareAmount != null && salaryAmount != null && salaryAmount.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal welfareRatio = welfareAmount.divide(salaryAmount, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            result.put("福利费占实发工资比例", welfareRatio.setScale(2, RoundingMode.HALF_UP));
        }

        return result;
    }

    /**
     * 解析Dify工作流返回结果，提取think_process和answer_content
     *
     * @param entity 财务分析实体
     * @param outputText Dify工作流详情JSON字符串
     */
    private void parseDifyWorkflowResult(FinanceAnalysisEntity entity, String outputText) {
        try {
            log.info("开始解析Dify工作流结果: workflowRunId={}", entity.getWorkflowRunId());


            // 提取think_process和answer_content
            String thinkProcess = extractThinkProcess(outputText);
            String answerContent = extractAnswerContent(outputText);

            // 设置到实体中
            entity.setThinkProcess(thinkProcess);
            entity.setAnswerContent(answerContent);

            log.info("Dify工作流结果解析完成: workflowRunId={}, thinkProcess长度={}, answerContent长度={}",
                    entity.getWorkflowRunId(),
                    thinkProcess != null ? thinkProcess.length() : 0,
                    answerContent != null ? answerContent.length() : 0);

        } catch (Exception e) {
            log.error("解析Dify工作流结果失败: workflowRunId={}", entity.getWorkflowRunId(), e);
        }
    }

    /**
     * 从Dify结果中提取思考过程
     */
    private String extractThinkProcess(String outputText) {
        try {
            StringBuilder thinkContent = new StringBuilder();
            String extracted = extractThinkBetweenTags(outputText, "think");
            if (extracted != null && !extracted.trim().isEmpty()) {
                if (thinkContent.length() > 0) {
                    thinkContent.append("\n\n");
                }
                thinkContent.append(extracted);
            }
            return thinkContent.length() > 0 ? thinkContent.toString() : null;

        } catch (Exception e) {
            log.error("提取思考过程失败", e);
            return null;
        }
    }

    /**
     * 从Dify结果中提取回复内容
     */
    private String extractAnswerContent(String outputText) {
        try {
            StringBuilder answerContent = new StringBuilder();

            // 按指定顺序提取分析节点内容
            String[] analysisNodes = {
                    "key_expenses_analysis",
                    "tax_profit_analysis",
                    "cigarette_operation_analysis",
                    "three_expenses_analysis"
            };

            for (String nodeName : analysisNodes) {
                String extracted = extractContentBetweenTags(outputText, nodeName);
                if (extracted != null && !extracted.trim().isEmpty()) {
                    if (answerContent.length() > 0) {
                        answerContent.append("\n\n");
                    }
                    answerContent.append(extracted);
                }
            }

            return answerContent.length() > 0 ? answerContent.toString() : null;

        } catch (Exception e) {
            log.error("提取回复内容失败", e);
            return null;
        }
    }

    /**
     * 提取标签之间的内容
     */
    private String extractThinkBetweenTags(String text, String tagName) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }

        String startTag = "<" + tagName + ">";
        String endTag = "</" + tagName + ">";

        int startIndex = text.indexOf(startTag);
        if (startIndex == -1) {
            return null;
        }

        int contentStart = startIndex + startTag.length();
        int endIndex = text.indexOf(endTag, contentStart);
        if (endIndex == -1) {
            return null;
        }

        return text.substring(contentStart, endIndex).trim();
    }

    /**
     * 提取标签之间的内容
     */
    private String extractContentBetweenTags(String text, String tagName) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }

        String startTag = "<" + tagName + ">";
        String endTag = "</" + tagName + ">";

        int startIndex = text.lastIndexOf(startTag);
        if (startIndex == -1) {
            return null;
        }

        int contentStart = startIndex + startTag.length();
        int endIndex = text.indexOf(endTag, contentStart);
        if (endIndex == -1) {
            return null;
        }

        return text.substring(contentStart, endIndex).trim();
    }

}