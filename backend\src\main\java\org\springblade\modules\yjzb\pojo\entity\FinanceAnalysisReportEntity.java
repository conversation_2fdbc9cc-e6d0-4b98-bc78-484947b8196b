/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.util.Date;

/**
 * 财务分析报告列表实体类
 *
 * <AUTHOR> Assistant
 */
@Data
@TableName("yjzb_finance_analysis_report")
@Schema(description = "财务分析报告列表对象")
@EqualsAndHashCode(callSuper = true)
public class FinanceAnalysisReportEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 报告标题
     */
    @Schema(description = "报告标题")
    private String title;

    /**
     * 报告类型
     */
    @Schema(description = "报告类型")
    private String type;

    /**
     * 分析周期
     */
    @Schema(description = "分析周期")
    private String period;

    /**
     * 查询年份
     */
    @Schema(description = "查询年份")
    private Integer queryYear;

    /**
     * 对比年份
     */
    @Schema(description = "对比年份")
    private Integer compareYear;

    /**
     * 开始月份
     */
    @Schema(description = "开始月份")
    private Integer startMonth;

    /**
     * 结束月份
     */
    @Schema(description = "结束月份")
    private Integer endMonth;

    /**
     * 状态：generating-生成中，completed-已完成，failed-失败
     */
    @Schema(description = "状态")
    private String reportStatus;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 文件名称
     */
    @Schema(description = "文件名称")
    private String fileName;

    /**
     * 文件大小(字节)
     */
    @Schema(description = "文件大小(字节)")
    private Long fileSize;

    /**
     * 下载次数
     */
    @Schema(description = "下载次数")
    private Integer downloadCount;

    /**
     * 生成时间
     */
    @Schema(description = "生成时间")
    private Date generateTime;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    private Date completeTime;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * PDF文件路径
     */
    @Schema(description = "PDF文件路径")
    private String pdfFilePath;

    /**
     * PDF文件名称
     */
    @Schema(description = "PDF文件名称")
    private String pdfFileName;

    /**
     * PDF文件大小(字节)
     */
    @Schema(description = "PDF文件大小(字节)")
    private Long pdfFileSize;
}
