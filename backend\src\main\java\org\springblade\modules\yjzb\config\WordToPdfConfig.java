package org.springblade.modules.yjzb.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Word转PDF配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "word-to-pdf")
public class WordToPdfConfig {

    /**
     * 是否启用Pandoc转换
     */
    private boolean enablePandoc = true;

    /**
     * 是否启用LibreOffice转换
     */
    private boolean enableLibreOffice = true;

    /**
     * 是否启用docx4j转换（回退方案）
     */
    private boolean enableDocx4j = true;

    /**
     * 转换超时时间（秒）
     */
    private int timeoutSeconds = 60;

    /**
     * LibreOffice可执行文件路径（可选，系统PATH中有则不需要配置）
     */
    private String libreOfficePath = "libreoffice";

    /**
     * Pandoc可执行文件路径（可选，系统PATH中有则不需要配置）
     */
    private String pandocPath = "pandoc";

    /**
     * PDF引擎选择（用于Pandoc）
     * 可选值：xelatex, pdflatex, lualatex, wkhtmltopdf
     */
    private String pdfEngine = "xelatex";

    /**
     * 中文字体设置（用于Pandoc）
     */
    private String cjkFont = "SimSun";

    /**
     * 页边距设置
     */
    private String margin = "2cm";
}
