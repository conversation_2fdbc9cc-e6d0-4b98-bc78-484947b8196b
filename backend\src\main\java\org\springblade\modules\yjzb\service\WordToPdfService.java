package org.springblade.modules.yjzb.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.yjzb.config.WordToPdfConfig;
import org.springblade.modules.yjzb.util.PandocWordToPdfConverter;
import org.springblade.modules.yjzb.util.TikaWordToPdfConverter;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Word转PDF服务类
 * 统一管理多种转换策略，提供最佳的转换体验
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WordToPdfService {

    private final WordToPdfConfig config;

    /**
     * 将Word文档转换为PDF
     * @param wordBytes Word文档字节数组
     * @return PDF字节数组
     * @throws Exception 转换异常
     */
    public byte[] convertWordToPdf(byte[] wordBytes) throws Exception {
        log.info("开始Word转PDF转换，文件大小: {} bytes", wordBytes.length);
        
        Exception lastException = null;
        
        // 策略1: Pandoc（最佳质量，支持复杂格式）
        if (config.isEnablePandoc() && PandocWordToPdfConverter.isPandocAvailable()) {
            try {
                log.info("使用Pandoc进行Word转PDF转换");
                return PandocWordToPdfConverter.convertWordToPdf(wordBytes);
            } catch (Exception e) {
                log.warn("Pandoc转换失败: {}", e.getMessage());
                lastException = e;
            }
        }
        
        // 策略2: LibreOffice（高质量，广泛支持）
        if (config.isEnableLibreOffice()) {
            try {
                log.info("使用LibreOffice进行Word转PDF转换");
                return convertWithLibreOffice(wordBytes);
            } catch (Exception e) {
                log.warn("LibreOffice转换失败: {}", e.getMessage());
                lastException = e;
            }
        }
        
        // 策略3: Tika + PDFBox（纯Java，轻量级）
        if (config.isEnableTika()) {
            try {
                log.info("使用Tika+PDFBox进行Word转PDF转换");
                return convertWithTika(wordBytes);
            } catch (Exception e) {
                log.warn("Tika转换失败: {}", e.getMessage());
                lastException = e;
            }
        }
        
        // 策略4: docx4j（最后的回退方案，有超时控制）
        if (config.isEnableDocx4j()) {
            try {
                log.info("使用docx4j进行Word转PDF转换（回退方案）");
                return convertWithDocx4jTimeout(wordBytes);
            } catch (Exception e) {
                log.error("docx4j转换也失败: {}", e.getMessage());
                lastException = e;
            }
        }
        
        // 所有策略都失败
        throw new Exception("所有Word转PDF转换策略都失败", lastException);
    }

    /**
     * 使用LibreOffice进行转换
     */
    private byte[] convertWithLibreOffice(byte[] wordBytes) throws Exception {
        File tempDir = null;
        File wordFile = null;
        File pdfFile = null;
        
        try {
            tempDir = Files.createTempDirectory("libreoffice_convert").toFile();
            wordFile = new File(tempDir, "input.docx");
            
            // 写入Word文件
            try (FileOutputStream fos = new FileOutputStream(wordFile)) {
                fos.write(wordBytes);
            }
            
            // 构建LibreOffice命令
            ProcessBuilder pb = new ProcessBuilder(
                config.getLibreOfficePath(),
                "--headless",
                "--convert-to", "pdf",
                "--outdir", tempDir.getAbsolutePath(),
                wordFile.getAbsolutePath()
            );
            
            pb.directory(tempDir);
            Process process = pb.start();
            
            // 等待转换完成
            boolean finished = process.waitFor(config.getTimeoutSeconds(), TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new Exception("LibreOffice转换超时");
            }
            
            if (process.exitValue() != 0) {
                String error = new String(process.getErrorStream().readAllBytes(), StandardCharsets.UTF_8);
                throw new Exception("LibreOffice转换失败: " + error);
            }
            
            // 查找生成的PDF文件
            pdfFile = new File(tempDir, "input.pdf");
            if (!pdfFile.exists()) {
                throw new Exception("PDF文件未生成");
            }
            
            return Files.readAllBytes(pdfFile.toPath());
            
        } finally {
            cleanupFiles(tempDir, wordFile, pdfFile);
        }
    }

    /**
     * 使用Tika + PDFBox进行转换
     */
    private byte[] convertWithTika(byte[] wordBytes) throws Exception {
        try (ByteArrayInputStream input = new ByteArrayInputStream(wordBytes);
             ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            
            TikaWordToPdfConverter.convertWordToPdf(input, output);
            return output.toByteArray();
        }
    }

    /**
     * 使用docx4j进行转换（带超时控制）
     */
    private byte[] convertWithDocx4jTimeout(byte[] wordBytes) throws Exception {
        CompletableFuture<byte[]> future = CompletableFuture.supplyAsync(() -> {
            try (ByteArrayInputStream input = new ByteArrayInputStream(wordBytes);
                 ByteArrayOutputStream output = new ByteArrayOutputStream()) {
                
                org.springblade.modules.yjzb.util.DocxToPdfConverter.convertDocxToPdf(input, output);
                return output.toByteArray();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        try {
            return future.get(config.getTimeoutSeconds(), TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            future.cancel(true);
            throw new Exception("docx4j转换超时");
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupFiles(File... files) {
        for (File file : files) {
            if (file != null && file.exists()) {
                try {
                    if (file.isDirectory()) {
                        Files.walk(file.toPath())
                             .sorted((a, b) -> b.compareTo(a))
                             .forEach(path -> {
                                 try {
                                     Files.delete(path);
                                 } catch (IOException e) {
                                     log.debug("删除临时文件失败: {}", path, e);
                                 }
                             });
                    } else {
                        file.delete();
                    }
                } catch (Exception e) {
                    log.debug("清理临时文件失败: {}", file.getAbsolutePath(), e);
                }
            }
        }
    }

    /**
     * 检查转换能力
     */
    public String getAvailableConverters() {
        StringBuilder sb = new StringBuilder();
        
        if (config.isEnablePandoc() && PandocWordToPdfConverter.isPandocAvailable()) {
            sb.append("Pandoc, ");
        }
        
        if (config.isEnableLibreOffice()) {
            sb.append("LibreOffice, ");
        }
        
        if (config.isEnableTika()) {
            sb.append("Tika+PDFBox, ");
        }
        
        if (config.isEnableDocx4j()) {
            sb.append("docx4j");
        }
        
        return sb.toString().replaceAll(", $", "");
    }
}
