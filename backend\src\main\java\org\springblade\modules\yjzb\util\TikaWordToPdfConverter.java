package org.springblade.modules.yjzb.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.microsoft.ooxml.OOXMLParser;
import org.apache.tika.sax.BodyContentHandler;
import org.xml.sax.SAXException;

import java.io.*;

/**
 * 使用Apache Tika + PDFBox进行Word转PDF
 * 这是一个轻量级的纯Java解决方案
 */
@Slf4j
public class TikaWordToPdfConverter {

    /**
     * 将Word文档转换为PDF
     * @param wordInputStream Word文档输入流
     * @param pdfOutputStream PDF输出流
     * @throws Exception 转换异常
     */
    public static void convertWordToPdf(InputStream wordInputStream, OutputStream pdfOutputStream) throws Exception {
        try {
            // 使用Tika提取Word文档内容
            String textContent = extractTextFromWord(wordInputStream);
            
            // 使用PDFBox创建PDF文档
            createPdfFromText(textContent, pdfOutputStream);
            
        } catch (Exception e) {
            log.error("Tika Word转PDF失败", e);
            throw new Exception("Word转PDF转换失败", e);
        }
    }

    /**
     * 从Word文档中提取文本内容
     */
    private static String extractTextFromWord(InputStream wordInputStream) throws IOException, SAXException, TikaException {
        BodyContentHandler handler = new BodyContentHandler(-1); // 不限制内容长度
        Metadata metadata = new Metadata();
        ParseContext parseContext = new ParseContext();
        OOXMLParser parser = new OOXMLParser();
        
        parser.parse(wordInputStream, handler, metadata, parseContext);
        return handler.toString();
    }

    /**
     * 从文本内容创建PDF文档
     */
    private static void createPdfFromText(String textContent, OutputStream pdfOutputStream) throws IOException {
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage();
            document.addPage(page);
            
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                contentStream.setFont(PDType1Font.HELVETICA, 12);
                contentStream.beginText();
                contentStream.setLeading(14.5f);
                contentStream.newLineAtOffset(50, 750);
                
                // 分行处理文本内容
                String[] lines = textContent.split("\\r?\\n");
                float yPosition = 750;
                
                for (String line : lines) {
                    if (yPosition < 50) {
                        // 需要新页面
                        contentStream.endText();
                        contentStream.close();
                        
                        page = new PDPage();
                        document.addPage(page);
                        PDPageContentStream newContentStream = new PDPageContentStream(document, page);
                        newContentStream.setFont(PDType1Font.HELVETICA, 12);
                        newContentStream.beginText();
                        newContentStream.setLeading(14.5f);
                        newContentStream.newLineAtOffset(50, 750);
                        yPosition = 750;
                        contentStream = newContentStream;
                    }
                    
                    // 处理长行，自动换行
                    if (line.length() > 80) {
                        String[] words = line.split(" ");
                        StringBuilder currentLine = new StringBuilder();
                        
                        for (String word : words) {
                            if (currentLine.length() + word.length() > 80) {
                                if (currentLine.length() > 0) {
                                    contentStream.showText(currentLine.toString());
                                    contentStream.newLine();
                                    yPosition -= 14.5f;
                                    currentLine = new StringBuilder();
                                }
                            }
                            currentLine.append(word).append(" ");
                        }
                        
                        if (currentLine.length() > 0) {
                            contentStream.showText(currentLine.toString());
                            contentStream.newLine();
                            yPosition -= 14.5f;
                        }
                    } else {
                        contentStream.showText(line);
                        contentStream.newLine();
                        yPosition -= 14.5f;
                    }
                }
                
                contentStream.endText();
            }
            
            document.save(pdfOutputStream);
        }
    }
}
