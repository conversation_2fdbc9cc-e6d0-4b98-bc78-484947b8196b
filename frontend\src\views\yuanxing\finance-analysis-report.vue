<template>
  <!-- 财务税费管控 - 深度分析报告页面 -->
  <basic-container>
    <!-- 页面标题和操作 -->
    <div class="page-header">
      <h3>深度分析报告</h3>
      <div class="header-actions">
        <el-button type="primary" @click="generateReport">
          <el-icon><plus /></el-icon> 生成新报告
        </el-button>
        <el-button @click="refreshReports">
          <el-icon><refresh /></el-icon> 刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-panel">
      <el-form :model="filterForm" inline label-width="80px">
        <el-form-item label="选择月份">
          <el-date-picker
            v-model="filterForm.selectedMonth"
            type="month"
            placeholder="选择月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 报告统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon finance">
            <el-icon><document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">总报告数</div>
            <div class="stat-value">{{ reportStats.total }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon success">
            <el-icon><circle-check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">本年完成</div>
            <div class="stat-value">{{ reportStats.thisyear }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <el-icon><clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">生成中</div>
            <div class="stat-value">{{ reportStats.generating }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon danger">
            <el-icon><warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-title">失败重试</div>
            <div class="stat-value">{{ reportStats.failed }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 报告列表 -->
    <div class="report-list-panel">
      <div class="panel-header">
        <h4>分析报告列表</h4>
        <div class="panel-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索报告标题"
            size="small"
            prefix-icon="el-icon-search"
            style="width: 200px; margin-right: 10px;"
          />
          <el-select v-model="listSortType" size="small" style="width: 120px;">
            <el-option label="最新生成" value="latest" />
            <el-option label="最多下载" value="downloadCount" />
            <el-option label="按标题" value="title" />
          </el-select>
        </div>
      </div>
      
      <el-table :data="filteredReportList" stripe border v-loading="loading">
        <el-table-column prop="title" label="报告标题" min-width="200">
          <template #default="{ row }">
            <div class="report-title">
              <el-icon><document /></el-icon>
              <span @click="viewReport(row)" class="title-link">{{ row.title }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="报告类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getReportTypeTag(row.type)" size="small">
              {{ row.typeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="period" label="分析周期" width="140" />
        <el-table-column prop="generateTime" label="生成时间" width="160" />
        <el-table-column prop="fileSize" label="文件大小（KB）" width="130" />
        <el-table-column prop="downloadCount" label="下载次数" width="100" align="right" />
        <el-table-column prop="reportStatus" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.reportStatus)" size="small">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="mini" type="text" @click="viewReport(row)" v-if="row.reportStatus === 'completed'">
              <el-icon><view /></el-icon> 查看
            </el-button>
            <el-button size="mini" type="text" @click="downloadReport(row)" v-if="row.reportStatus === 'completed'">
              <el-icon><download /></el-icon> 下载
            </el-button>
            <el-button size="mini" type="text" @click="retryGenerate(row)" v-if="row.reportStatus === 'failed'">
              <el-icon><refresh /></el-icon> 重试
            </el-button>
            <el-button size="mini" type="text" @click="deleteReport(row)">
              <el-icon><delete /></el-icon> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div style="margin-top: 15px; text-align: right;">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 报告详情对话框 -->
    <el-dialog
      v-model="reportDialogVisible"
      :title="currentReport?.title"
      width="820px"
      :modal-append-to-body="false"
      top="5vh"
      destroy-on-close
      @close="handleDialogClose"
    >
      <div class="report-detail-template" v-if="currentReport">
        <div class="template-header">
          <h2>广东烟草阳江市有限责任公司</h2>
          <h3>{{ currentReport.title }}</h3>
          <div class="template-meta">
            <span>分析周期：{{ currentReport.period }}</span>
            <span>生成时间：{{ currentReport.generateTime }}</span>
            <span>下载次数：{{ currentReport.downloadCount }}</span>
          </div>
          <div class="template-actions">
            <el-button @click="downloadReport(currentReport)">
              <el-icon><download /></el-icon> 下载报告
            </el-button>
            <el-button @click="exportReport('pdf')">
              <el-icon><document /></el-icon> 导出PDF
            </el-button>
            <!-- 移除导出Excel按钮 -->
          </div>
        </div>
        <div class="template-content">
          <div v-html="currentReport.templateContent" class="template-html"></div>
        </div>
      </div>
    </el-dialog>

    <!-- 生成报告对话框 -->
    <el-dialog
      v-model="generateDialogVisible"
      title="生成分析报告"
      width="600px"
    >
      <el-form :model="generateForm" :rules="generateRules" ref="generateFormRef" label-width="120px">
        <el-form-item label="报告标题" prop="title">
          <el-input v-model="generateForm.title" placeholder="请输入报告标题" />
        </el-form-item>
        <el-form-item label="报告类型" prop="type">
          <el-select v-model="generateForm.type" placeholder="选择报告类型">
            <el-option label="财务分析报告" value="finance" />
            <el-option label="税务分析报告" value="tax" />
            <el-option label="费用分析报告" value="expense" />
            <el-option label="预算分析报告" value="budget" />
            <el-option label="风险分析报告" value="risk" />
          </el-select>
        </el-form-item>
        <el-form-item label="查询年份" prop="queryYear">
          <el-select v-model="generateForm.queryYear" placeholder="选择查询年份">
            <el-option v-for="year in yearOptions" :key="year" :label="year + '年'" :value="year" />
          </el-select>
        </el-form-item>
        <el-form-item label="对比年份" prop="compareYear">
          <el-select v-model="generateForm.compareYear" placeholder="选择对比年份">
            <el-option v-for="year in yearOptions" :key="year" :label="year + '年'" :value="year" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始月份" prop="startMonth">
          <el-select v-model="generateForm.startMonth" placeholder="选择开始月份">
            <el-option v-for="month in monthOptions" :key="month" :label="month + '月'" :value="month" />
          </el-select>
        </el-form-item>
        <el-form-item label="结束月份" prop="endMonth">
          <el-select v-model="generateForm.endMonth" placeholder="选择结束月份">
            <el-option v-for="month in monthOptions" :key="month" :label="month + '月'" :value="month" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="generateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitGenerate" :loading="generating">
          {{ generating ? '生成中...' : '开始生成' }}
        </el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import { Plus, Refresh, Document, CircleCheck, Clock, Warning, View, Download, Share, Delete, Calendar, Files } from '@element-plus/icons-vue'
import {
  getReportList,
  getReportStatistics,
  getReportDetail,
  generateReport,
  removeReports,
  downloadReport,
  retryGenerateReport,
  previewReport,
  downloadPdfReport
} from '@/api/yjzb/financeAnalysisReport'

export default {
  name: 'FinanceAnalysisReport',
  components: {
    Plus, Refresh, Document, CircleCheck, Clock, Warning, View, Download, Share, Delete, Calendar, Files
  },
  data() {
    return {
      loading: false,
      searchKeyword: '',
      listSortType: 'latest',
      pageSize: 10,
      currentPage: 1,
      total: 0,
      reportDialogVisible: false,
      generateDialogVisible: false,
      currentReport: null,
      activeTab: 'summary',
      generating: false,

      // 筛选表单
      filterForm: {
        selectedMonth: '',
        type: '',
        reportStatus: ''
      },

      // 报告统计
      reportStats: {
        total: 0,
        thisYear: 0,
        generating: 0,
        failed: 0
      },

      // 报告列表数据
      reportList: [],

      // 年份选项
      yearOptions: [],

      // 月份选项
      monthOptions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      
      // 生成报告表单
      generateForm: {
        title: '',
        type: 'finance',
        queryYear: new Date().getFullYear(),
        compareYear: new Date().getFullYear() - 1,
        startMonth: 1,
        endMonth: 12
      },
      
      // 表单验证规则
      generateRules: {
        title: [
          { required: true, message: '请输入报告标题', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择报告类型', trigger: 'change' }
        ],
        queryYear: [
          { required: true, message: '请选择查询年份', trigger: 'change' }
        ],
        compareYear: [
          { required: true, message: '请选择对比年份', trigger: 'change' }
        ],
        startMonth: [
          { required: true, message: '请选择开始月份', trigger: 'change' }
        ],
        endMonth: [
          { required: true, message: '请选择结束月份', trigger: 'change' }
        ]
      }
    }
  },

  mounted() {
    this.initYearOptions()
    this.loadReportList()
    this.loadStatistics()
  },

  beforeUnmount() {
    // 清理文件URL对象
    this.cleanupFileUrl()
    // 清理全局下载函数
    if (window.downloadCurrentFile) {
      delete window.downloadCurrentFile
    }
  },

  watch: {
    // 监听搜索关键词变化
    searchKeyword: {
      handler() {
        this.currentPage = 1
        this.loadReportList()
      },
      deep: true
    },

    // 监听筛选条件变化
    filterForm: {
      handler() {
        this.currentPage = 1
        this.loadReportList()
      },
      deep: true
    }
  },
  computed: {
    filteredReportList() {
      return this.reportList
    }
  },
  methods: {
    // 清理文件URL对象
    cleanupFileUrl() {
      if (this.currentReport && this.currentReport.fileUrl) {
        window.URL.revokeObjectURL(this.currentReport.fileUrl)
        this.currentReport.fileUrl = null
      }
    },

    // 对话框关闭处理
    handleDialogClose() {
      this.cleanupFileUrl()
      // 清理全局下载函数
      if (window.downloadCurrentFile) {
        delete window.downloadCurrentFile
      }
    },

    // 初始化年份选项
    initYearOptions() {
      const currentYear = new Date().getFullYear()
      this.yearOptions = []
      for (let i = currentYear; i >= currentYear - 10; i--) {
        this.yearOptions.push(i)
      }
    },

    // 加载报告列表
    async loadReportList() {
      try {
        this.loading = true
        const params = {
          current: this.currentPage,
          size: this.pageSize,
          title: this.searchKeyword,
          type: this.filterForm.type,
          reportStatus: this.filterForm.reportStatus
        }

        const response = await getReportList(params)
        if (response.data.success) {
          this.reportList = response.data.data.records || []
          this.total = response.data.data.total || 0
        }
      } catch (error) {
        console.error('加载报告列表失败:', error)
        this.$message.error('加载报告列表失败')
      } finally {
        this.loading = false
      }
    },

    // 加载统计信息
    async loadStatistics() {
      try {
        const response = await getReportStatistics()
        if (response.data.success) {
          this.reportStats = response.data.data || {}
        }
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },

    // 生成新报告
    generateReport() {
      this.generateDialogVisible = true
      this.generateForm = {
        title: '',
        type: 'finance',
        queryYear: new Date().getFullYear(),
        compareYear: new Date().getFullYear() - 1,
        startMonth: 1,
        endMonth: 12
      }
    },
    
    // 提交生成报告
    async submitGenerate() {
      try {
        const valid = await this.$refs.generateFormRef.validate()
        if (valid) {
          this.generating = true

          const response = await generateReport(this.generateForm)
          if (response.data.success) {
            this.generating = false
            this.generateDialogVisible = false
            this.$message.success('报告生成任务已提交，请稍后查看结果')
            this.loadReportList()
            this.loadStatistics()
          } else {
            this.$message.error(response.data.msg || '生成报告失败')
          }
        }
      } catch (error) {
        console.error('生成报告失败:', error)
        this.$message.error('生成报告失败')
      } finally {
        this.generating = false
      }
    },
    
    // 查看报告详情 - 改为下载文件预览
    async viewReport(row) {
      try {
        if (row.reportStatus !== 'completed') {
          this.$message.warning('报告尚未生成完成，无法预览')
          return
        }

        // 显示加载状态
        this.$message.info('正在加载预览...')

        // 尝试获取PDF预览
        let previewUrl = null
        let fileName = row.fileName || `${row.title}.docx`
        let fileExtension = fileName.split('.').pop().toLowerCase()

        try {
          // 首先尝试获取PDF预览（后端将Word转换为PDF）
          const previewResponse = await previewReport(row.id)
          const pdfBlob = new Blob([previewResponse.data], { type: 'application/pdf' })
          previewUrl = window.URL.createObjectURL(pdfBlob)
          fileExtension = 'pdf' // 标记为PDF用于预览
        } catch (previewError) {
          console.warn('PDF预览失败，尝试原文件预览:', previewError)
          // 如果PDF预览失败，回退到原文件
          const response = await downloadReport(row.id)
          const blob = new Blob([response.data])
          previewUrl = window.URL.createObjectURL(blob)
        }

        // 设置当前报告信息
        this.currentReport = {
          ...row,
          fileName: fileName,
          fileUrl: previewUrl,
          fileExtension: fileExtension
        }

        // 根据文件类型生成预览内容
        if (fileExtension === 'pdf') {
          // PDF文件使用iframe预览（包括转换后的PDF）
          this.currentReport.templateContent = `
            <div style="width: 100%; height: 700px; border: 1px solid #ddd; border-radius: 4px; background: #f5f5f5;">
              <iframe src="${previewUrl}" width="100%" height="100%" frameborder="0" style="border-radius: 4px; background: white;">
                <div style="text-align: center; padding: 40px;">
                  <p>您的浏览器不支持PDF预览</p>
                  <button onclick="window.open('${previewUrl}', '_blank')" style="
                    background: #409EFF;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                  ">在新窗口打开</button>
                </div>
              </iframe>
            </div>
            <div style="text-align: center; margin-top: 16px;">
              <button onclick="window.downloadOriginalFile()" style="
                background: #67C23A;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
              ">
                <i class="el-icon-download"></i> 下载原文件
              </button>
            </div>
          `
          // 设置下载原文件函数
          window.downloadOriginalFile = async () => {
            try {
              const response = await downloadReport(row.id)
              const blob = new Blob([response.data])
              const url = window.URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = url
              link.download = fileName
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              window.URL.revokeObjectURL(url)
              this.$message.success('下载成功')
            } catch (error) {
              this.$message.error('下载失败')
            }
          }
        } else if (['doc', 'docx'].includes(fileExtension)) {
          // Word文档显示提示信息和下载链接
          this.currentReport.templateContent = `
            <div style="text-align: center; padding: 40px;">
              <div style="font-size: 48px; color: #409EFF; margin-bottom: 20px;">
                <i class="el-icon-document"></i>
              </div>
              <h3 style="color: #303133; margin-bottom: 16px;">Word文档预览</h3>
              <p style="color: #606266; margin-bottom: 24px;">
                文件名：${fileName}<br/>
                PDF预览服务暂时不可用，请下载原文件查看
              </p>
              <button onclick="window.downloadCurrentFile()" style="
                background: #409EFF;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
              ">
                <i class="el-icon-download"></i> 下载文件
              </button>
            </div>
          `
          // 设置全局下载函数
          window.downloadCurrentFile = () => {
            const link = document.createElement('a')
            link.href = previewUrl
            link.download = fileName
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.$message.success('下载成功')
          }
        } else {
          // 其他文件类型显示通用预览
          this.currentReport.templateContent = `
            <div style="text-align: center; padding: 40px;">
              <div style="font-size: 48px; color: #909399; margin-bottom: 20px;">
                <i class="el-icon-files"></i>
              </div>
              <h3 style="color: #303133; margin-bottom: 16px;">文件预览</h3>
              <p style="color: #606266; margin-bottom: 24px;">
                文件名：${fileName}<br/>
                文件类型：${fileExtension.toUpperCase()}
              </p>
              <button onclick="window.open('${previewUrl}', '_blank')" style="
                background: #409EFF;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                margin-right: 12px;
              ">
                <i class="el-icon-view"></i> 在新窗口打开
              </button>
              <button onclick="window.downloadCurrentFile()" style="
                background: #67C23A;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
              ">
                <i class="el-icon-download"></i> 下载文件
              </button>
            </div>
          `
          // 设置全局下载函数
          window.downloadCurrentFile = () => {
            const link = document.createElement('a')
            link.href = previewUrl
            link.download = fileName
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.$message.success('下载成功')
          }
        }

        // 显示对话框
        this.reportDialogVisible = true
        this.activeTab = 'summary'

        // 刷新列表以更新下载次数
        this.loadReportList()
      } catch (error) {
        console.error('预览报告失败:', error)
        this.$message.error('预览报告失败')
      }
    },

    // 查看报告模板（保留原有的模板展示功能）
    viewReportTemplate(row) {
      this.currentReport = {
        ...row,
        templateContent: `广东烟草阳江市有限责任公司<br/>
2025年1-5月财务分析报告<br/><br/>
2025年1-5月，在省局（公司）和市局（公司）党组的正确领导下，全市烟草系统人员上下一心，卷烟结构不断优化，卷烟经营保持良好市场状态，各项经济指标详见表1。<br/>
<br/>
<b>表1. 阳江烟草2025年1-5月主要经济指标</b><br/>
<table border='1' cellpadding='4' cellspacing='0' style='border-collapse:collapse;'>
<tr><th>项目</th><th>2025年1-5月</th><th>2024年1-5月</th><th>同比增减(%)</th><th>2025年预算数</th><th>预算执行进度(%)</th></tr>
<tr><td>税利总额</td><td>15,678.90</td><td>13,456.78</td><td>16.50</td><td>28,900.00</td><td>54.25</td></tr>
<tr><td>其中：利润总额</td><td>7,890.12</td><td>7,123.45</td><td>10.78</td><td>15,000.00</td><td>52.60</td></tr>
<tr><td>税费（不含所得税）</td><td>8,888.88</td><td>7,654.32</td><td>16.14</td><td>13,333.33</td><td>66.67</td></tr>
<tr><td>卷烟销售数量</td><td>22,500.00</td><td>21,000.00</td><td>7.14</td><td>45,000.00</td><td>50.00</td></tr>
<tr><td>卷烟销售收入</td><td>65,000.00</td><td>62,000.00</td><td>4.84</td><td>130,000.00</td><td>50.00</td></tr>
<tr><td>卷烟销售成本</td><td>32,000.00</td><td>31,000.00</td><td>3.23</td><td>65,000.00</td><td>49.23</td></tr>
<tr><td>毛利润</td><td>33,000.00</td><td>31,000.00</td><td>6.45</td><td>65,000.00</td><td>50.77</td></tr>
<tr><td>毛利率(%)</td><td>50.77</td><td>50.00</td><td>0.77</td><td>50.00</td><td>—</td></tr>
<tr><td>其他业务收入</td><td>120.00</td><td>110.00</td><td>9.09</td><td>210.00</td><td>57.14</td></tr>
<tr><td>其他业务成本</td><td>60.00</td><td>55.00</td><td>9.09</td><td>110.00</td><td>54.55</td></tr>
<tr><td>三项费用总额</td><td>2,200.00</td><td>2,300.00</td><td>-4.35</td><td>4,200.00</td><td>52.38</td></tr>
<tr><td>三项费用率(%)</td><td>6.67</td><td>7.12</td><td>-0.45</td><td>6.67</td><td>—</td></tr>
</table><br/>
备注：2025年1-5月，各项指标预算执行进度应达到41.67%以上。<br/><br/>
<b>一、实现税利情况分析</b><br/>
2025年1-5月，阳江市烟草系统实现税利50234万元，同比增加2345万元，增长了4.90%，其中：实现利润总额20012万元，同比增长5.12%；应交税金（不含企业所得税）合计30222万元，同比增长4.56%。<br/>
我公司全年税利预算数为90000万元，截至2024年5月31日，实际执行进度为55.82%，比时间进度41.67%超出14.15个百分点。<br/><br/>
<b>二、卷烟经营情况分析</b><br/>
（一）卷烟销售收入和卷烟销量<br/>
2025年1-5月，阳江市烟草系统实现卷烟销售收入（不含税）159000万元，同比增加4000万元，增长2.58%。累计实现卷烟销量41000箱，同比增加500箱，增长1.23%。<br/>
（二）卷烟结构<br/>
2025年1-5月，阳江市烟草系统单箱收入（含税）43900元/箱，同比提高800元/箱，增长1.86%，比公司年度预算目标42000元/箱高1900元/箱。<br/>
（三）卷烟销售毛利和毛利率<br/>
2025年1-5月，阳江市烟草系统实现卷烟销售毛利47000万元，同比增长2.75%。实现平均卷烟毛利率31.00%，同比增长0.10个百分点，超过毛利率预算数30.50%。<br/><br/>
<b>三、费用支出情况分析</b><br/>
（一）三项费用支出总体情况<br/>
2025年1-5月，全市烟草系统三项费用支出合计6200万元，同比增加200万元，增长3.33%。其中：销售费用1800万元，管理费用4500万元，财务费用-100万元。三项费用率4.80%，比上年同期增长0.03个百分点（详见表2）。<br/>
<b>表2. 阳江烟草2025年1-5月三项费用</b><br/>
<table border='1' cellpadding='4' cellspacing='0' style='border-collapse:collapse;'>
<tr><th>预算项目</th><th>行次</th><th>2025年1-5月</th><th>2024年1-5月</th><th>增减额</th><th>增减比率%</th><th>2025年预算数</th><th>预算余额</th><th>预算执行率%</th></tr>
<tr><td>合计</td><td>2</td><td>6,200.00</td><td>6,000.00</td><td>200.00</td><td>3.33</td><td>18,500.00</td><td>12,300.00</td><td>33.51</td></tr>
<tr><td>修理费</td><td>3</td><td>130.00</td><td>70.00</td><td>60.00</td><td>85.71</td><td>260.00</td><td>130.00</td><td>50.00</td></tr>
<tr><td>财务费用</td><td>4</td><td>-100.00</td><td>-150.00</td><td>50.00</td><td>-33.33</td><td>-350.00</td><td>-250.00</td><td>28.57</td></tr>
<tr><td>专卖打假经费</td><td>5</td><td>60.00</td><td>25.00</td><td>35.00</td><td>140.00</td><td>130.00</td><td>70.00</td><td>46.15</td></tr>
<tr><td>专卖打私经费</td><td>6</td><td>10.00</td><td>2.00</td><td>8.00</td><td>400.00</td><td>35.00</td><td>25.00</td><td>28.57</td></tr>
<tr><td>企业文化建设费</td><td>7</td><td>18.00</td><td>12.00</td><td>6.00</td><td>50.00</td><td>45.00</td><td>27.00</td><td>40.00</td></tr>
<tr><td>中介费</td><td>8</td><td>35.00</td><td>28.00</td><td>7.00</td><td>25.00</td><td>110.00</td><td>75.00</td><td>31.82</td></tr>
<tr><td>无形资产摊销</td><td>9</td><td>95.00</td><td>90.00</td><td>5.00</td><td>5.56</td><td>210.00</td><td>115.00</td><td>45.24</td></tr>
<tr><td>劳务费</td><td>10</td><td>320.00</td><td>300.00</td><td>20.00</td><td>6.67</td><td>750.00</td><td>430.00</td><td>42.67</td></tr>
<tr><td>绿化费</td><td>11</td><td>5.00</td><td>2.00</td><td>3.00</td><td>150.00</td><td>18.00</td><td>13.00</td><td>27.78</td></tr>
<tr><td>业务招待费</td><td>12</td><td>3.00</td><td>1.00</td><td>2.00</td><td>200.00</td><td>4.00</td><td>1.00</td><td>75.00</td></tr>
<tr><td>会议费</td><td>13</td><td>5.00</td><td>4.00</td><td>1.00</td><td>25.00</td><td>6.00</td><td>1.00</td><td>83.33</td></tr>
<tr><td>协会会费</td><td>14</td><td>1.00</td><td>0.50</td><td>0.50</td><td>100.00</td><td>12.00</td><td>11.00</td><td>8.33</td></tr>
<tr><td>零售终端建设费</td><td>15</td><td>0</td><td>0</td><td>0</td><td>-</td><td>110.00</td><td>110.00</td><td>0</td></tr>
<tr><td>文明吸烟环境建设费</td><td>16</td><td>0</td><td>0</td><td>0</td><td>-</td><td>3.00</td><td>3.00</td><td>0</td></tr>
<tr><td>交易手续费</td><td>17</td><td>0</td><td>0</td><td>0</td><td>-</td><td>55.00</td><td>55.00</td><td>0</td></tr>
<tr><td>长期待摊费用摊销</td><td>18</td><td>3.00</td><td>2.00</td><td>1.00</td><td>50.00</td><td>12.00</td><td>9.00</td><td>25.00</td></tr>
<tr><td>政府性基金</td><td>19</td><td>0</td><td>0</td><td>0</td><td>-</td><td>12.00</td><td>12.00</td><td>0</td></tr>
<tr><td>劳动保护费</td><td>20</td><td>0</td><td>0.20</td><td>-0.20</td><td>-100.00</td><td>55.00</td><td>55.00</td><td>0</td></tr>
<tr><td>书报费</td><td>21</td><td>0.40</td><td>0.50</td><td>-0.10</td><td>-20.00</td><td>22.00</td><td>21.60</td><td>1.82</td></tr>
<tr><td>通讯费</td><td>22</td><td>16.00</td><td>18.00</td><td>-2.00</td><td>-11.11</td><td>65.00</td><td>49.00</td><td>24.62</td></tr>
<tr><td>车杂费</td><td>23</td><td>6.00</td><td>7.00</td><td>-1.00</td><td>-14.29</td><td>18.00</td><td>12.00</td><td>33.33</td></tr>
<tr><td>租赁费</td><td>24</td><td>130.00</td><td>135.00</td><td>-5.00</td><td>-3.70</td><td>320.00</td><td>190.00</td><td>40.63</td></tr>
<tr><td>物业管理费</td><td>25</td><td>6.00</td><td>7.00</td><td>-1.00</td><td>-14.29</td><td>18.00</td><td>12.00</td><td>33.33</td></tr>
<tr><td>保险费</td><td>26</td><td>3.00</td><td>4.00</td><td>-1.00</td><td>-25.00</td><td>22.00</td><td>19.00</td><td>13.64</td></tr>
<tr><td>信息系统维护费</td><td>27</td><td>0</td><td>3.00</td><td>-3.00</td><td>-</td><td>55.00</td><td>55.00</td><td>0</td></tr>
<tr><td>水电费</td><td>28</td><td>28.00</td><td>30.00</td><td>-2.00</td><td>-6.67</td><td>85.00</td><td>57.00</td><td>32.94</td></tr>
<tr><td>其他</td><td>29</td><td>2.00</td><td>6.00</td><td>-4.00</td><td>-66.67</td><td>12.00</td><td>10.00</td><td>16.67</td></tr>
<tr><td>网络通讯费</td><td>30</td><td>16.00</td><td>22.00</td><td>-6.00</td><td>-27.27</td><td>45.00</td><td>29.00</td><td>35.56</td></tr>
<tr><td>燃料费</td><td>31</td><td>22.00</td><td>27.00</td><td>-5.00</td><td>-18.52</td><td>65.00</td><td>43.00</td><td>33.85</td></tr>
<tr><td>低值易耗品摊销</td><td>32</td><td>3.00</td><td>9.00</td><td>-6.00</td><td>-66.67</td><td>17.00</td><td>14.00</td><td>17.65</td></tr>
<tr><td>包装费</td><td>33</td><td>12.00</td><td>20.00</td><td>-8.00</td><td>-40.00</td><td>35.00</td><td>23.00</td><td>34.29</td></tr>
<tr><td>专卖管理经费</td><td>34</td><td>22.00</td><td>32.00</td><td>-10.00</td><td>-31.25</td><td>85.00</td><td>63.00</td><td>25.88</td></tr>
<tr><td>差旅费</td><td>35</td><td>18.00</td><td>32.00</td><td>-14.00</td><td>-43.75</td><td>65.00</td><td>47.00</td><td>27.69</td></tr>
<tr><td>企业研发费用</td><td>36</td><td>1.00</td><td>12.00</td><td>-11.00</td><td>-91.67</td><td>4.00</td><td>3.00</td><td>25.00</td></tr>
<tr><td>党组织工作经费</td><td>37</td><td>0</td><td>12.00</td><td>-12.00</td><td>-100.00</td><td>12.00</td><td>12.00</td><td>0</td></tr>
<tr><td>警卫消防费</td><td>38</td><td>65.00</td><td>85.00</td><td>-20.00</td><td>-23.53</td><td>210.00</td><td>145.00</td><td>30.95</td></tr>
<tr><td>折旧费</td><td>39</td><td>270.00</td><td>320.00</td><td>-50.00</td><td>-15.63</td><td>820.00</td><td>550.00</td><td>32.93</td></tr>
<tr><td>办公费</td><td>40</td><td>32.00</td><td>65.00</td><td>-33.00</td><td>-50.77</td><td>105.00</td><td>73.00</td><td>30.48</td></tr>
<tr><td>职工薪酬</td><td>41</td><td>4,800.00</td><td>4,700.00</td><td>100.00</td><td>2.13</td><td>13,500.00</td><td>8,700.00</td><td>35.56</td></tr>
</table><br/>
<br/>
<b>（二）重点费用支出情况</b><br/>
<table border='1' cellpadding='4' cellspacing='0' style='border-collapse:collapse;'>
<tr><th>重点费用</th><th>2025年1-5月</th><th>2024年1-5月</th><th>同比增减(%)</th><th>2025年预算数</th><th>执行进度(%)</th></tr>
<tr><td>两项费用率(%)</td><td>5.12</td><td>5.25</td><td>-0.13</td><td>6.10</td><td>83.93</td></tr>
<tr><td>会议费</td><td>3.5</td><td>4.5</td><td>-22.22</td><td>8.0</td><td>43.75</td></tr>
<tr><td>车辆运行费</td><td>28.00</td><td>26.00</td><td>7.69</td><td>52.00</td><td>53.85</td></tr>
<tr><td>（一）车辆修理费</td><td>1.50</td><td>1.20</td><td>25.00</td><td>2.80</td><td>53.57</td></tr>
<tr><td>（二）车辆保险费</td><td>1.00</td><td>1.10</td><td>-9.09</td><td>2.20</td><td>45.45</td></tr>
<tr><td>（三）车辆燃油费</td><td>13.00</td><td>16.00</td><td>-18.75</td><td>32.00</td><td>40.63</td></tr>
<tr><td>（四）学术费</td><td>3.00</td><td>3.50</td><td>-14.29</td><td>7.00</td><td>42.86</td></tr>
<tr><td>福利费</td><td>220.00</td><td>190.00</td><td>15.79</td><td>520.00</td><td>42.31</td></tr>
<tr><td>（一）在职人员福利费支出</td><td>160.00</td><td>150.00</td><td>6.67</td><td>420.00</td><td>38.10</td></tr>
<tr><td>（二）退休人员福利费支出</td><td>35.00</td><td>28.00</td><td>25.00</td><td>65.00</td><td>53.85</td></tr>
<tr><td>实发工资</td><td>2,200.00</td><td>2,100.00</td><td>4.76</td><td>4,200.00</td><td>52.38</td></tr>
<tr><td>福利费占实发工资比例(%)</td><td>10.00</td><td>9.05</td><td>0.95</td><td>12.38</td><td>80.78</td></tr>
<tr><td>零基建维修</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>
<tr><td>终端形象提升</td><td>0</td><td>0</td><td>0</td><td>110.00</td><td>0</td></tr>
<tr><td>信息化终端建设</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>
<tr><td>客户端</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>
<tr><td>文明劝烟环境建设</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>
<tr><td>研发费</td><td>1.20</td><td>2.20</td><td>-45.45</td><td>6.00</td><td>20.00</td></tr>
<tr><td>对外捐赠支出</td><td>0</td><td>0</td><td>0</td><td>22.00</td><td>0</td></tr>
<tr><td>（一）扶贫捐赠支出</td><td>0</td><td>0</td><td>0</td><td>11.00</td><td>0</td></tr>
<tr><td>（二）水源工程支出</td><td>0</td><td>0</td><td>0</td><td>11.00</td><td>0</td></tr>
</table><br/>
<br/>
<b>（三）截至2025年5月31日，需要提醒各单位、各部门注意的事项金额变动较大支出项目情况</b><br/>
1. 人事科<br/>
（1）劳务费：预算820万元，1-5月实际执行350万元，执行进度42.68%。同比增加8万元，主要是加班导致劳务费增加。<br/>
（2）职工薪酬：预算18000万元，1-5月实际执行6100万元，执行进度33.89%。其中：实发工资3700万元，同比减少40万元，减少1.07%。员工福利费预算1200万元，实际执行370万元，执行进度30.83%。<br/>
1. 专卖办<br/>
（1）专卖打假经费：预算260万元，1-5月实际执行75万元，执行进度28.85%。建议：加快费用报销进度；争取侦办更多打假案件。<br/>
（2）专卖打私经费：预算55万元，1-5月实际执行12万元，执行进度21.82%。建议：加快费用报销进度；争取侦办更多打私案件。<br/>
（3）专卖管理经费：预算160万元，1-5月实际执行35万元，执行进度21.88%。<br/>
1. 信息中心<br/>
信息系统维护费：预算130万元，1-5月尚未执行。建议：加快执行进度，加强信息系统维护项目管理。<br/>
1. 党建科<br/>
- 企业文化建设费：预算55万元，1-5月实际执行22万元，执行进度40.00%。<br/>
- 党组织经费：预算32万元，1-5月尚未执行，主要是根据上年工资比例计提。<br/>
1. 营销管理中心<br/>
（1）卷烟经营进销存方面：主营业务收入和成本执行进度约48%左右，延续开门红。<br/>
（2）零售终端建设费（重点费用）：预算140万元，1-5月尚未执行。<br/>
（3）交易手续费：年度预算85万元，1-5月尚未执行。<br/>
1. 物流配送中心<br/>
包装费：预算65万元，1-5月实际执行18万元，执行进度27.69%。<br/>
1. 安全管理科<br/>
警卫消防费：预算300万元，1-5月实际执行90万元，执行进度30.00%。<br/>
1. 办公室（除个别特殊项目外，以本部费用为主）<br/>
- 办公费（本部）：预算100万元，1-5月实际支出36万元，执行进度36.00%。<br/>
- 业务招待费（全市）：预算5万元，1-5月实际支出3万元，执行进度60.00%。<br/>
- 会议费（全市）：预算5万元，1-5月实际支出5万元，执行进度100.00%。<br/>
- 保险费（全市）：预算38万元，1-5月实际执行3万元，执行进度7.89%。<br/>
- 修理费（全市）：预算300万元，1-5月实际执行150万元，执行进度50.00%。<br/>
- 差旅费（市局）：预算130万元，1-5月执行20万元，执行进度15.38%。<br/>
- 低值易耗品摊销（市局）：预算9万元，1-5月实际执行3万元，执行进度33.33%。<br/>
- 燃料费（本部）：预算65万元，1-5月实际执行22万元，执行进度33.85%。<br/>
- 企业研发费用（全市）：预算6万，1-5月实际执行1万元，执行进度16.67%。<br/>
`      }
      this.reportDialogVisible = true
      this.activeTab = 'summary'
      // 增加下载次数
      row.downloadCount++
    },
    
    // 下载报告
    async downloadReport(row) {
      try {
        if (row.reportStatus !== 'completed') {
          this.$message.warning('报告尚未生成完成，无法下载')
          return
        }

        const response = await downloadReport(row.id)

        // 创建下载链接
        const blob = new Blob([response.data])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = row.fileName || `${row.title}.docx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('下载成功')

        // 刷新列表以更新下载次数
        this.loadReportList()
      } catch (error) {
        console.error('下载报告失败:', error)
        this.$message.error('下载报告失败')
      }
    },
    
    // 分享报告
    shareReport(row) {
      this.$message.success('分享链接已复制到剪贴板')
    },
    
    // 删除报告
    deleteReport(row) {
      this.$confirm('确认删除此报告？删除后无法恢复。', '确认删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await removeReports(row.id.toString())
          if (response.data.success) {
            this.$message.success('删除成功')
            this.loadReportList()
            this.loadStatistics()
          } else {
            this.$message.error(response.data.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除报告失败:', error)
          this.$message.error('删除报告失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    
    // 重试生成
    async retryGenerate(row) {
      try {
        const response = await retryGenerateReport(row.id)
        if (response.data.success) {
          this.$message.success('重新生成任务已提交')
          this.loadReportList()
          this.loadStatistics()
        } else {
          this.$message.error(response.data.msg || '重试失败')
        }
      } catch (error) {
        console.error('重试生成报告失败:', error)
        this.$message.error('重试生成报告失败')
      }
    },
    
    // 导出报告
    async exportReport(format) {
      if (!this.currentReport) {
        this.$message.error('请先选择要导出的报告')
        return
      }

      try {
        if (format === 'pdf') {
          this.$message.info('正在准备PDF文件...')

          // 下载PDF文件
          const response = await downloadPdfReport(this.currentReport.id)

          // 创建下载链接
          const blob = new Blob([response.data], { type: 'application/pdf' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = this.currentReport.pdfFileName || `${this.currentReport.title}.pdf`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          this.$message.success('PDF导出成功')
        } else {
          this.$message.success(`正在导出${format.toUpperCase()}格式报告...`)
        }
      } catch (error) {
        console.error('导出报告失败:', error)
        this.$message.error('导出报告失败')
      }
    },
    
    // 筛选和刷新
    handleFilter() {
      // 查询逻辑
    },
    
    resetFilter() {
      this.filterForm.selectedMonth = '';
    },
    
    refreshReports() {
      this.loadReportList()
      this.loadStatistics()
      this.$message.success('刷新成功')
    },
    
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadReportList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadReportList()
    },
    
    // 工具方法
    getReportTypeTag(type) {
      const typeMap = {
        finance: 'primary',
        tax: 'success',
        expense: 'warning',
        budget: 'info',
        risk: 'danger'
      }
      return typeMap[type] || 'info'
    },
    
    getStatusTag(status) {
      const statusMap = {
        completed: 'success',
        generating: 'warning',
        failed: 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    getPriorityTag(priority) {
      const priorityMap = {
        high: 'danger',
        medium: 'warning',
        low: 'info'
      }
      return priorityMap[priority] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    color: #303133;
  }
  
  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.filter-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  padding: 20px;
  display: flex;
  align-items: center;
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    
    &.finance {
      background: #e8f4fd;
      color: #409eff;
    }
    
    &.success {
      background: #e8f8f1;
      color: #67c23a;
    }
    
    &.warning {
      background: #fdf6ec;
      color: #e6a23c;
    }
    
    &.danger {
      background: #fef0f0;
      color: #f56c6c;
    }
  }
  
  .stat-content {
    flex: 1;
    
    .stat-title {
      color: #909399;
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .stat-value {
      color: #303133;
      font-size: 24px;
      font-weight: bold;
    }
  }
}

.report-list-panel {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  
  .panel-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h4 {
      margin: 0;
      color: #303133;
    }
    
    .panel-actions {
      display: flex;
      align-items: center;
    }
  }
}

.report-title {
  display: flex;
  align-items: center;
  
  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
  
  .title-link {
    color: #409eff;
    cursor: pointer;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.report-detail-template {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .template-header {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    width: 100%;
    max-width: 800px;
    box-sizing: border-box;
    text-align: center;
  }

  .template-content {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .template-html {
    max-width: 800px;
    width: 100%;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    padding: 32px 32px 32px 32px;
    box-sizing: border-box;
    text-align: left;
    font-size: 16px;
    color: #303133;
    max-height: 70vh;
    overflow-y: auto;
    display: block;
  }
}

.report-recommendations {
  .recommendation-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    
    .rec-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      h5 {
        margin: 0;
        color: #303133;
      }
    }
    
    .rec-description {
      color: #606266;
      line-height: 1.6;
      margin-bottom: 15px;
    }
    
    .rec-actions {
      margin-bottom: 15px;
      
      h6 {
        margin: 0 0 10px 0;
        color: #303133;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          color: #606266;
          margin-bottom: 5px;
        }
      }
    }
    
    .rec-impact {
      .impact-label {
        color: #909399;
      }
      
      .impact-value {
        color: #303133;
        font-weight: bold;
      }
    }
  }
}

.el-dialog {
  max-width: 820px !important;
}
</style>

