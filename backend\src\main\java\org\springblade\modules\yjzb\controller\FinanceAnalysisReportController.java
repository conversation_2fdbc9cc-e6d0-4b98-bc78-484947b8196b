/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.yjzb.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;

import org.springblade.modules.yjzb.pojo.dto.GenerateReportDTO;
import org.springblade.modules.yjzb.pojo.entity.FinanceAnalysisReportEntity;
import org.springblade.modules.yjzb.pojo.vo.FinanceAnalysisReportVO;
import org.springblade.modules.yjzb.service.IFinanceAnalysisReportService;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 财务分析报告列表控制器
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/yjzb/finance-analysis-report")
@RequiredArgsConstructor
@Tag(name = "财务分析报告管理", description = "财务分析报告列表管理接口")
public class FinanceAnalysisReportController extends BladeController {

    private final IFinanceAnalysisReportService reportService;

    /**
     * 分页查询报告列表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "分页查询", description = "分页查询财务分析报告列表")
    public R<IPage<FinanceAnalysisReportVO>> list(FinanceAnalysisReportVO report, Query query) {
        IPage<FinanceAnalysisReportVO> pages = reportService.selectReportPage(Condition.getPage(query), report);
        return R.data(pages);
    }

    /**
     * 获取报告统计信息
     */
    @GetMapping("/statistics")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "获取统计信息", description = "获取报告统计信息")
    public R<Map<String, Object>> getStatistics() {
        Map<String, Object> statistics = reportService.getReportStatistics();
        return R.data(statistics);
    }

    /**
     * 查看报告详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "查看详情", description = "查看报告详情")
    public R<FinanceAnalysisReportVO> detail(@Parameter(description = "报告ID") @PathVariable Long id) {
        FinanceAnalysisReportVO detail = reportService.getReportDetail(id);
        return R.data(detail);
    }

    /**
     * 生成新报告
     */
    @PostMapping("/generate")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "生成报告", description = "生成新的分析报告")
    public R<Long> generateReport(@Valid @RequestBody GenerateReportDTO request) {
        Long reportId = reportService.generateAnalysisReport(
                request.getTitle(),
                request.getType(),
                request.getQueryYear(),
                request.getCompareYear(),
                request.getStartMonth(),
                request.getEndMonth()
        );
        return R.data(reportId);
    }

    /**
     * 删除报告
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "删除报告", description = "删除报告（逻辑删除）")
    public R<Boolean> remove(@Parameter(description = "报告ID集合") @RequestParam String ids) {
        boolean success = reportService.deleteReports(ids);
        return R.status(success);
    }

    /**
     * 下载报告
     */
    @GetMapping("/download/{id}")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "下载报告", description = "下载报告文件")
    public void downloadReport(@Parameter(description = "报告ID") @PathVariable Long id, HttpServletResponse response) {
        try {
            FinanceAnalysisReportEntity report = reportService.getById(id);
            if (report == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("报告不存在");
                return;
            }

            if (!"completed".equals(report.getReportStatus())) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("报告尚未生成完成");
                return;
            }

            // 下载文件数据
            byte[] fileData = reportService.downloadReportData(id);
            if (fileData == null || fileData.length == 0) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("文件不存在或为空");
                return;
            }

            // 增加下载次数
            reportService.incrementDownloadCount(id);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setContentLength(fileData.length);

            // 处理文件名编码，支持中文文件名
            String fileName = Func.isNotBlank(report.getFileName()) ? report.getFileName() : report.getTitle() + ".docx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            // 写入文件数据到响应流
            try (OutputStream outputStream = response.getOutputStream()) {
                outputStream.write(fileData);
                outputStream.flush();
            }

        } catch (IOException e) {
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("下载文件时发生IO错误: " + e.getMessage());
            } catch (IOException ex) {
                // 忽略写入错误响应时的异常
            }
        } catch (Exception e) {
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("下载文件失败: " + e.getMessage());
            } catch (IOException ex) {
                // 忽略写入错误响应时的异常
            }
        }
    }

    /**
     * 重试生成报告
     */
    @PostMapping("/retry/{id}")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "重试生成", description = "重试生成失败的报告")
    public R<Boolean> retryGenerate(@Parameter(description = "报告ID") @PathVariable Long id) {
        FinanceAnalysisReportEntity report = reportService.getById(id);
        if (report == null) {
            return R.fail("报告不存在");
        }

        if (!"failed".equals(report.getReportStatus())) {
            return R.fail("只能重试失败的报告");
        }

        // 重新生成报告
        Long newReportId = reportService.generateAnalysisReport(
                report.getTitle(),
                report.getType(),
                report.getQueryYear(),
                report.getCompareYear(),
                report.getStartMonth(),
                report.getEndMonth()
        );

        return R.data(newReportId != null);
    }
}
