package org.springblade.modules.yjzb.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.TimeUnit;

/**
 * 使用Pandoc进行Word转PDF
 * Pandoc是一个强大的文档转换工具，支持多种格式
 * 需要系统安装pandoc: https://pandoc.org/installing.html
 */
@Slf4j
public class PandocWordToPdfConverter {

    /**
     * 使用Pandoc将Word转换为PDF
     * @param wordBytes Word文档字节数组
     * @return PDF字节数组
     * @throws Exception 转换异常
     */
    public static byte[] convertWordToPdf(byte[] wordBytes) throws Exception {
        Path tempDir = null;
        Path wordFile = null;
        Path pdfFile = null;
        
        try {
            // 创建临时目录
            tempDir = Files.createTempDirectory("pandoc_convert");
            
            // 创建临时Word文件
            wordFile = tempDir.resolve("input.docx");
            Files.write(wordFile, wordBytes);
            
            // 输出PDF文件路径
            pdfFile = tempDir.resolve("output.pdf");
            
            // 构建Pandoc命令
            ProcessBuilder pb = new ProcessBuilder(
                "pandoc",
                wordFile.toString(),
                "-o", pdfFile.toString(),
                "--pdf-engine=xelatex",  // 使用xelatex引擎，支持中文
                "-V", "CJKmainfont=SimSun",  // 设置中文字体
                "-V", "geometry:margin=2cm"   // 设置页边距
            );
            
            pb.directory(tempDir.toFile());
            pb.redirectErrorStream(true);
            
            log.info("执行Pandoc转换命令: {}", String.join(" ", pb.command()));
            
            Process process = pb.start();
            
            // 读取输出信息
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            // 等待转换完成，最多等待60秒
            boolean finished = process.waitFor(60, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new Exception("Pandoc转换超时");
            }
            
            if (process.exitValue() != 0) {
                log.error("Pandoc转换失败，输出信息: {}", output.toString());
                throw new Exception("Pandoc转换失败: " + output.toString());
            }
            
            // 检查PDF文件是否生成
            if (!Files.exists(pdfFile)) {
                throw new Exception("PDF文件未生成");
            }
            
            // 读取PDF文件内容
            byte[] pdfBytes = Files.readAllBytes(pdfFile);
            log.info("Pandoc转换成功，PDF大小: {} bytes", pdfBytes.length);
            
            return pdfBytes;
            
        } finally {
            // 清理临时文件
            cleanupTempFiles(tempDir, wordFile, pdfFile);
        }
    }

    /**
     * 清理临时文件
     */
    private static void cleanupTempFiles(Path... paths) {
        for (Path path : paths) {
            if (path != null && Files.exists(path)) {
                try {
                    if (Files.isDirectory(path)) {
                        // 删除目录及其内容
                        Files.walk(path)
                             .sorted((a, b) -> b.compareTo(a)) // 先删除文件，后删除目录
                             .forEach(p -> {
                                 try {
                                     Files.delete(p);
                                 } catch (IOException e) {
                                     log.warn("删除临时文件失败: {}", p, e);
                                 }
                             });
                    } else {
                        Files.delete(path);
                    }
                } catch (IOException e) {
                    log.warn("清理临时文件失败: {}", path, e);
                }
            }
        }
    }

    /**
     * 检查Pandoc是否可用
     */
    public static boolean isPandocAvailable() {
        try {
            ProcessBuilder pb = new ProcessBuilder("pandoc", "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            return finished && process.exitValue() == 0;
        } catch (Exception e) {
            log.debug("Pandoc不可用: {}", e.getMessage());
            return false;
        }
    }
}
