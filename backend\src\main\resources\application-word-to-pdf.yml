# Word转PDF配置示例
word-to-pdf:
  # 启用/禁用各种转换器
  enable-pandoc: true          # 是否启用Pandoc转换（最佳质量）
  enable-libre-office: true   # 是否启用LibreOffice转换（高质量）
  enable-tika: true           # 是否启用Tika+PDFBox转换（纯Java）
  enable-docx4j: true         # 是否启用docx4j转换（回退方案）
  
  # 转换超时设置
  timeout-seconds: 60         # 转换超时时间（秒）
  
  # 可执行文件路径（可选，如果在系统PATH中则不需要配置）
  libre-office-path: "libreoffice"  # LibreOffice可执行文件路径
  pandoc-path: "pandoc"             # Pandoc可执行文件路径
  
  # Pandoc特定配置
  pdf-engine: "xelatex"       # PDF引擎：xelatex, pdflatex, lualatex, wkhtmltopdf
  cjk-font: "SimSun"          # 中文字体
  margin: "2cm"               # 页边距

# 使用说明：
# 1. Pandoc方案（推荐）：
#    - 安装Pandoc: https://pandoc.org/installing.html
#    - 安装LaTeX发行版（如MiKTeX或TeX Live）用于PDF生成
#    - 优点：转换质量最高，格式保持最好
#    - 缺点：需要额外安装软件
#
# 2. LibreOffice方案：
#    - 安装LibreOffice: https://www.libreoffice.org/download/
#    - 优点：转换质量高，广泛支持
#    - 缺点：需要额外安装软件，启动较慢
#
# 3. Tika+PDFBox方案：
#    - 纯Java实现，无需额外安装
#    - 优点：部署简单，轻量级
#    - 缺点：转换质量一般，格式可能丢失
#
# 4. docx4j方案（回退）：
#    - 纯Java实现，无需额外安装
#    - 优点：部署简单
#    - 缺点：容易卡死，转换质量一般

# 推荐配置策略：
# - 生产环境：启用Pandoc + LibreOffice，禁用docx4j
# - 开发环境：启用所有方案进行测试
# - 容器环境：可以在Docker镜像中预装Pandoc和LibreOffice
